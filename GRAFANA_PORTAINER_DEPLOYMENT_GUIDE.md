# 🚀 Grafana Deployment Guide for Portainer

## 📋 **Two Configuration Options Available**

### Option 1: **Complete Monitoring Stack** (`grafana-portainer-stack.yml`)
- Grafana + Prometheus + Node Exporter + cAdvisor + Redis
- Full monitoring solution with auto-connected datasources
- Recommended for comprehensive monitoring

### Option 2: **Grafana Only** (`grafana-only-portainer.yml`)
- Standalone Grafana installation
- Pre-configured for SoloYLibre Ultimate
- Lighter deployment, can connect to external data sources

## 🎯 **Deployment Steps in Portainer**

### Step 1: Access Portainer
1. Open **Portainer**: http://localhost:9000
2. Login with your admin credentials
3. Navigate to **"Stacks"** in the left sidebar
4. Click **"Add stack"**

### Step 2: Configure Stack
1. **Name**: `soloylibre_ultimate_grafana`
2. **Build method**: Select **"Web editor"**
3. **Copy and paste** one of the configuration files below

### Step 3: Deploy
1. Click **"Deploy the stack"**
2. Wait for deployment to complete
3. Check **"Containers"** section to verify all services are running

## 📁 **Configuration Files**

### 🔧 **Option 1: Complete Stack** (Recommended)
```yaml
# Copy contents from: grafana-portainer-stack.yml
# Includes: Grafana + Prometheus + Node Exporter + cAdvisor + Redis
```

### 🔧 **Option 2: Grafana Only**
```yaml
# Copy contents from: grafana-only-portainer.yml
# Includes: Grafana standalone with setup container
```

## 🔐 **Login Credentials**

### **Grafana Access**
- **URL**: http://localhost:3000
- **Username**: `soloylibre_ultimate`
- **Password**: `PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?`
- **Email**: <EMAIL>

## 🌐 **Service Ports**

### **Complete Stack**
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090
- **Node Exporter**: http://localhost:9100
- **cAdvisor**: http://localhost:8080
- **Redis**: localhost:6379

### **Grafana Only**
- **Grafana**: http://localhost:3000

## ✅ **Features Included**

### **Grafana Configuration**
- ✅ Pre-configured admin user (soloylibre_ultimate)
- ✅ Custom SoloYLibre branding
- ✅ Essential plugins pre-installed
- ✅ Dark theme by default
- ✅ Security hardening
- ✅ Welcome dashboard included
- ✅ Timezone set to America/New_York

### **Monitoring Stack Features** (Option 1)
- ✅ Auto-connected Prometheus datasource
- ✅ System metrics collection (Node Exporter)
- ✅ Container metrics collection (cAdvisor)
- ✅ Redis for caching and sessions
- ✅ Health checks for all services
- ✅ Proper service dependencies

### **Owner Customization**
- ✅ **Owner**: Jose L Encarnacion (JoseTusabe)
- ✅ **Email**: <EMAIL>
- ✅ **Phone**: ************
- ✅ **Location**: San Jose de Ocoa, Dom. Rep.
- ✅ **Server**: Synology RS3618xs | 56GB RAM | 36TB Storage
- ✅ **Passion**: Photography and Technology

## 🔧 **Post-Deployment Steps**

### 1. **Access Grafana**
1. Open http://localhost:3000
2. Login with credentials above
3. Explore the welcome dashboard

### 2. **Configure Data Sources** (Grafana Only option)
1. Go to **Configuration** → **Data Sources**
2. Add Prometheus: http://your-prometheus-url:9090
3. Add other data sources as needed

### 3. **Import Dashboards**
1. Go to **"+"** → **Import**
2. Use dashboard IDs from Grafana.com:
   - **Node Exporter**: 1860
   - **Docker**: 893
   - **Prometheus**: 3662

### 4. **Create Custom Dashboards**
1. Click **"+"** → **Dashboard**
2. Add panels for your specific monitoring needs
3. Save and organize in folders

## 🛠️ **Management Commands**

### **Through Portainer Interface**
- **Start/Stop**: Use container controls in Portainer
- **View Logs**: Click on container → "Logs"
- **Update**: Recreate stack with newer images
- **Backup**: Export stack configuration

### **Direct Docker Commands** (if needed)
```bash
# View running containers
docker ps --filter "name=soloylibre_ultimate"

# View logs
docker logs soloylibre_ultimate_grafana_josetusabe

# Restart service
docker restart soloylibre_ultimate_grafana_josetusabe
```

## 🔄 **Backup & Maintenance**

### **Data Persistence**
- All data stored in Docker volumes
- Configuration persisted across restarts
- Dashboards and settings preserved

### **Updates**
1. In Portainer, go to **Stacks**
2. Select your stack
3. Click **"Editor"**
4. Update image tags if needed
5. Click **"Update the stack"**

## 📞 **Support Information**

### **Owner Contact**
- **Name**: Jose L Encarnacion (JoseTusabe)
- **Email**: <EMAIL>
- **Phone**: ************
- **Location**: San Jose de Ocoa, Dom. Rep.

### **Websites**
- [SoloYLibre](https://soloylibre.com)
- [JoseTusabe](https://josetusabe.com)
- [1and1Photo](https://1and1photo.com)
- [Jose L Encarnacion](https://joselencarnacion.com)

### **Environment**
- **Server**: Synology RS3618xs
- **Memory**: 56GB RAM
- **Storage**: 36TB
- **Purpose**: JEYKO AI Development & Photography Workflows

---

## 🎉 **Ready to Deploy!**

**Choose your preferred option and deploy through Portainer interface!**

*Developed with ❤️ by Jose L Encarnacion (JoseTusabe)*  
*Passionate about Photography and Technology*
