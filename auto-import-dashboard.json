{"dashboard": {"id": null, "uid": "soloylibre-wordpress-ultimate", "title": "🎉 SoloYLibre Ultimate - WordPress Complete Monitoring", "description": "Complete WordPress monitoring dashboard for Jose L Encarnacion (JoseTusabe) - Auto-configured", "tags": ["wordpress", "<PERSON><PERSON><PERSON><PERSON>", "jose<PERSON><PERSON>", "ultimate", "monitoring"], "timezone": "America/New_York", "editable": true, "graphTooltip": 1, "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "WordPress Site", "tooltip": "Open WordPress Site", "type": "link", "url": "http://localhost:1051"}, {"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "WordPress Admin", "tooltip": "Open WordPress Admin", "type": "link", "url": "http://localhost:1051/wp-admin"}, {"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "phpMyAdmin", "tooltip": "Open phpMyAdmin", "type": "link", "url": "http://localhost:2051"}], "panels": [{"id": 1, "title": "🎯 SoloYLibre Ultimate WordPress Overview", "type": "text", "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "options": {"mode": "markdown", "content": "# 🚀 SoloYLibre Ultimate WordPress Monitoring\n\n**Owner:** <PERSON> (JoseTusabe) | **Email:** <EMAIL> | **Phone:** ************ | **Location:** San Jose <PERSON>, Dom. Rep. | **Server:** Synology RS3618xs (56GB RAM, 36TB) | **Passion:** Photography & Technology 📸💻"}}, {"id": 2, "title": "🟢 WordPress Status", "type": "stat", "gridPos": {"h": 4, "w": 4, "x": 0, "y": 3}, "targets": [{"expr": "up{job=\"cadvisor\", name=~\".*wordpress.*\"}", "legendFormat": "WordPress", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}}, "type": "value"}, {"options": {"1": {"text": "UP", "color": "green"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center"}}, {"id": 3, "title": "🗄️ Database Status", "type": "stat", "gridPos": {"h": 4, "w": 4, "x": 4, "y": 3}, "targets": [{"expr": "up{job=\"cadvisor\", name=~\".*db.*\"}", "legendFormat": "MariaDB", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}}, "type": "value"}, {"options": {"1": {"text": "UP", "color": "green"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center"}}, {"id": 4, "title": "🔴 <PERSON><PERSON>", "type": "stat", "gridPos": {"h": 4, "w": 4, "x": 8, "y": 3}, "targets": [{"expr": "up{job=\"cadvisor\", name=~\".*redis.*\"}", "legendFormat": "Redis", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}}, "type": "value"}, {"options": {"1": {"text": "UP", "color": "green"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center"}}, {"id": 5, "title": "🔧 phpMyAdmin", "type": "stat", "gridPos": {"h": 4, "w": 4, "x": 12, "y": 3}, "targets": [{"expr": "up{job=\"cadvisor\", name=~\".*phpmyadmin.*\"}", "legendFormat": "phpMyAdmin", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}}, "type": "value"}, {"options": {"1": {"text": "UP", "color": "green"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center"}}, {"id": 6, "title": "💾 Total Memory Usage", "type": "stat", "gridPos": {"h": 4, "w": 4, "x": 16, "y": 3}, "targets": [{"expr": "sum(container_memory_usage_bytes{name=~\"soloylibre_ultimate.*\"}) / 1024 / 1024", "legendFormat": "Total MB", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 2000}]}, "unit": "decbytes"}}, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center"}}, {"id": 7, "title": "⚡ Total CPU Usage", "type": "stat", "gridPos": {"h": 4, "w": 4, "x": 20, "y": 3}, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{name=~\"soloylibre_ultimate.*\"}[5m])) * 100", "legendFormat": "Total CPU %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 80}]}, "unit": "percent"}}, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center"}}, {"id": 8, "title": "📈 WordPress CPU Usage Over Time", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "targets": [{"expr": "rate(container_cpu_usage_seconds_total{name=~\".*wordpress.*\"}[5m]) * 100", "legendFormat": "WordPress CPU %", "refId": "A"}, {"expr": "rate(container_cpu_usage_seconds_total{name=~\".*db.*\"}[5m]) * 100", "legendFormat": "Database CPU %", "refId": "B"}, {"expr": "rate(container_cpu_usage_seconds_total{name=~\".*redis.*\"}[5m]) * 100", "legendFormat": "Redis CPU %", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "CPU %", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}, "unit": "percent"}}}, {"id": 9, "title": "💾 Memory Usage Over Time", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "targets": [{"expr": "container_memory_usage_bytes{name=~\".*wordpress.*\"} / 1024 / 1024", "legendFormat": "WordPress Memory MB", "refId": "A"}, {"expr": "container_memory_usage_bytes{name=~\".*db.*\"} / 1024 / 1024", "legendFormat": "Database Memory MB", "refId": "B"}, {"expr": "container_memory_usage_bytes{name=~\".*redis.*\"} / 1024 / 1024", "legendFormat": "Redis Memory MB", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Memory MB", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}, "unit": "decbytes"}}}, {"id": 10, "title": "🌐 Network Traffic", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 15}, "targets": [{"expr": "rate(container_network_receive_bytes_total{name=~\".*wordpress.*\"}[5m])", "legendFormat": "WordPress RX", "refId": "A"}, {"expr": "rate(container_network_transmit_bytes_total{name=~\".*wordpress.*\"}[5m])", "legendFormat": "WordPress TX", "refId": "B"}, {"expr": "rate(container_network_receive_bytes_total{name=~\".*db.*\"}[5m])", "legendFormat": "Database RX", "refId": "C"}, {"expr": "rate(container_network_transmit_bytes_total{name=~\".*db.*\"}[5m])", "legendFormat": "Database TX", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bytes/sec", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}, "unit": "binBps"}}}]}}