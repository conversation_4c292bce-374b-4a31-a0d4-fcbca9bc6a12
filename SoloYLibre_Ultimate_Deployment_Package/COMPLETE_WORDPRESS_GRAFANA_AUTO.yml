version: '3.9'

services:
  wordpress:
    image: wordpress:php8.2
    container_name: soloylibre_ultimate_wordpress_josetusabe
    hostname: soloylibre-ultimate-wordpress
    ports:
      - 1051:80
    depends_on:
      - db
      - redis
    volumes:
      - wordpress_data:/var/www/html:rw
    environment:
      TZ: America/New_York
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: soloylibre_ultimate
      WORDPRESS_DB_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      WORDPRESS_DB_NAME: soloylibre_ultimate_db
      WORDPRESS_TABLE_PREFIX: wp_soloylibre_
      WORDPRESS_DEBUG: 0
    restart: unless-stopped

  db:
    image: mariadb:11.3-jammy
    container_name: soloylibre_ultimate_wordpress_db_josetusabe
    hostname: soloylibre-ultimate-wordpress-db
    security_opt:
      - no-new-privileges:true
    environment:
      TZ: America/New_York
      MYSQL_DATABASE: soloylibre_ultimate_db
      MYSQL_USER: soloylibre_ultimate
      MYSQL_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      MYSQL_ROOT_PASSWORD: SoloYLibre2024RootDB!JoseTusabe
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql:rw
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=200
      --innodb_buffer_pool_size=512M
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: soloylibre_ultimate_redis_josetusabe
    hostname: soloylibre-ultimate-redis
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data:rw
    command: redis-server --appendonly yes --requirepass "SoloYLibre2024Redis"
    environment:
      TZ: America/New_York
    restart: unless-stopped

  phpmyadmin:
    image: phpmyadmin:latest
    container_name: soloylibre_ultimate_phpmyadmin_josetusabe
    hostname: soloylibre-ultimate-phpmyadmin
    ports:
      - 2051:80
    depends_on:
      - db
    environment:
      TZ: America/New_York
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: soloylibre_ultimate
      PMA_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      MYSQL_ROOT_PASSWORD: SoloYLibre2024RootDB!JoseTusabe
      PMA_ARBITRARY: 1
      UPLOAD_LIMIT: 1024M
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre_ultimate_prometheus_josetusabe
    hostname: soloylibre-ultimate-prometheus
    ports:
      - 9090:9090
    depends_on:
      - node-exporter
      - cadvisor
    volumes:
      - prometheus_data:/prometheus:rw
      - prometheus_config:/etc/prometheus:rw
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    environment:
      TZ: America/New_York
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:latest
    container_name: soloylibre_ultimate_node_exporter_josetusabe
    hostname: soloylibre-ultimate-node-exporter
    ports:
      - 9100:9100
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    environment:
      TZ: America/New_York
    restart: unless-stopped

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: soloylibre_ultimate_cadvisor_josetusabe
    hostname: soloylibre-ultimate-cadvisor
    ports:
      - 8080:8080
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    environment:
      TZ: America/New_York
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    ports:
      - 3000:3000
    depends_on:
      - prometheus
      - redis
    volumes:
      - grafana_data:/var/lib/grafana:rw
      - grafana_provisioning:/etc/grafana/provisioning:rw
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_DEFAULT_THEME: dark
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-worldmap-panel,redis-datasource,mysql-datasource
    restart: unless-stopped

  grafana-setup:
    image: alpine:latest
    container_name: soloylibre_ultimate_grafana_setup_josetusabe
    hostname: soloylibre-ultimate-grafana-setup
    depends_on:
      - grafana
      - prometheus
    volumes:
      - grafana_provisioning:/etc/grafana/provisioning:rw
      - prometheus_config:/etc/prometheus:rw
    environment:
      TZ: America/New_York
    command: >
      sh -c "
      echo '🚀 Setting up SoloYLibre Ultimate WordPress + Grafana Auto-Connection...' &&
      apk add --no-cache curl &&
      sleep 30 &&
      
      # Create Prometheus configuration
      mkdir -p /etc/prometheus &&
      cat > /etc/prometheus/prometheus.yml << 'EOF'
      global:
        scrape_interval: 15s
        evaluation_interval: 15s
        external_labels:
          monitor: 'soloylibre-wordpress-monitor'
          owner: 'josetusabe'
      
      scrape_configs:
        - job_name: 'prometheus'
          static_configs:
            - targets: ['localhost:9090']
      
        - job_name: 'node-exporter'
          static_configs:
            - targets: ['soloylibre-ultimate-node-exporter:9100']
      
        - job_name: 'cadvisor'
          static_configs:
            - targets: ['soloylibre-ultimate-cadvisor:8080']
      
        - job_name: 'wordpress-containers'
          static_configs:
            - targets: ['soloylibre-ultimate-cadvisor:8080']
          metrics_path: '/metrics'
          params:
            container: ['soloylibre_ultimate_wordpress_josetusabe', 'soloylibre_ultimate_wordpress_db_josetusabe', 'soloylibre_ultimate_redis_josetusabe']
      EOF
      
      # Create Grafana datasources
      mkdir -p /etc/grafana/provisioning/datasources &&
      cat > /etc/grafana/provisioning/datasources/datasources.yml << 'EOF'
      apiVersion: 1
      datasources:
        - name: Prometheus
          type: prometheus
          access: proxy
          url: http://soloylibre-ultimate-prometheus:9090
          isDefault: true
          editable: true
          jsonData:
            timeInterval: '5s'
            queryTimeout: '60s'
            httpMethod: 'POST'
          version: 1
        - name: MySQL
          type: mysql
          access: proxy
          url: soloylibre-ultimate-wordpress-db:3306
          database: soloylibre_ultimate_db
          user: soloylibre_ultimate
          isDefault: false
          editable: true
          secureJsonData:
            password: 'PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?'
          version: 1
        - name: Redis
          type: redis-datasource
          access: proxy
          url: redis://soloylibre-ultimate-redis:6379
          isDefault: false
          editable: true
          secureJsonData:
            password: 'SoloYLibre2024Redis'
          version: 1
      EOF
      
      # Create dashboard provisioning
      mkdir -p /etc/grafana/provisioning/dashboards &&
      mkdir -p /var/lib/grafana/dashboards &&
      cat > /etc/grafana/provisioning/dashboards/dashboards.yml << 'EOF'
      apiVersion: 1
      providers:
        - name: 'SoloYLibre WordPress Dashboards'
          orgId: 1
          folder: 'WordPress'
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards
      EOF

      # Create the ultimate WordPress dashboard
      cat > /var/lib/grafana/dashboards/wordpress-ultimate.json << 'DASHBOARD_EOF'
      {
        \"dashboard\": {
          \"id\": null,
          \"uid\": \"soloylibre-wordpress-ultimate\",
          \"title\": \"🎉 SoloYLibre Ultimate - WordPress Complete Monitoring\",
          \"description\": \"Complete WordPress monitoring dashboard for Jose L Encarnacion (JoseTusabe) - Auto-configured\",
          \"tags\": [\"wordpress\", \"soloylibre\", \"josetusabe\", \"ultimate\", \"monitoring\"],
          \"timezone\": \"America/New_York\",
          \"editable\": true,
          \"graphTooltip\": 1,
          \"time\": {
            \"from\": \"now-1h\",
            \"to\": \"now\"
          },
          \"refresh\": \"30s\",
          \"links\": [
            {
              \"title\": \"WordPress Site\",
              \"type\": \"link\",
              \"url\": \"http://localhost:1051\",
              \"targetBlank\": true
            },
            {
              \"title\": \"WordPress Admin\",
              \"type\": \"link\",
              \"url\": \"http://localhost:1051/wp-admin\",
              \"targetBlank\": true
            },
            {
              \"title\": \"phpMyAdmin\",
              \"type\": \"link\",
              \"url\": \"http://localhost:2051\",
              \"targetBlank\": true
            }
          ],
          \"panels\": [
            {
              \"id\": 1,
              \"title\": \"🎯 SoloYLibre Ultimate WordPress Overview\",
              \"type\": \"text\",
              \"gridPos\": {\"h\": 3, \"w\": 24, \"x\": 0, \"y\": 0},
              \"options\": {
                \"mode\": \"markdown\",
                \"content\": \"# 🚀 SoloYLibre Ultimate WordPress Monitoring\\n\\n**Owner:** Jose L Encarnacion (JoseTusabe) | **Email:** <EMAIL> | **Phone:** ************ | **Location:** San Jose de Ocoa, Dom. Rep. | **Server:** Synology RS3618xs (56GB RAM, 36TB) | **Passion:** Photography & Technology 📸💻\"
              }
            },
            {
              \"id\": 2,
              \"title\": \"🟢 WordPress Status\",
              \"type\": \"stat\",
              \"gridPos\": {\"h\": 4, \"w\": 6, \"x\": 0, \"y\": 3},
              \"targets\": [
                {
                  \"expr\": \"up{job=\\\"cadvisor\\\", name=~\\\".*wordpress.*\\\"}\",
                  \"legendFormat\": \"WordPress\",
                  \"refId\": \"A\"
                }
              ],
              \"fieldConfig\": {
                \"defaults\": {
                  \"color\": {\"mode\": \"thresholds\"},
                  \"mappings\": [
                    {\"options\": {\"0\": {\"text\": \"DOWN\", \"color\": \"red\"}}, \"type\": \"value\"},
                    {\"options\": {\"1\": {\"text\": \"UP\", \"color\": \"green\"}}, \"type\": \"value\"}
                  ],
                  \"thresholds\": {
                    \"steps\": [
                      {\"color\": \"red\", \"value\": null},
                      {\"color\": \"green\", \"value\": 1}
                    ]
                  }
                }
              },
              \"options\": {
                \"colorMode\": \"background\",
                \"graphMode\": \"none\",
                \"justifyMode\": \"center\"
              }
            },
            {
              \"id\": 3,
              \"title\": \"🗄️ Database Status\",
              \"type\": \"stat\",
              \"gridPos\": {\"h\": 4, \"w\": 6, \"x\": 6, \"y\": 3},
              \"targets\": [
                {
                  \"expr\": \"up{job=\\\"cadvisor\\\", name=~\\\".*db.*\\\"}\",
                  \"legendFormat\": \"MariaDB\",
                  \"refId\": \"A\"
                }
              ],
              \"fieldConfig\": {
                \"defaults\": {
                  \"color\": {\"mode\": \"thresholds\"},
                  \"mappings\": [
                    {\"options\": {\"0\": {\"text\": \"DOWN\", \"color\": \"red\"}}, \"type\": \"value\"},
                    {\"options\": {\"1\": {\"text\": \"UP\", \"color\": \"green\"}}, \"type\": \"value\"}
                  ],
                  \"thresholds\": {
                    \"steps\": [
                      {\"color\": \"red\", \"value\": null},
                      {\"color\": \"green\", \"value\": 1}
                    ]
                  }
                }
              },
              \"options\": {
                \"colorMode\": \"background\",
                \"graphMode\": \"none\",
                \"justifyMode\": \"center\"
              }
            },
            {
              \"id\": 4,
              \"title\": \"🔴 Redis Cache\",
              \"type\": \"stat\",
              \"gridPos\": {\"h\": 4, \"w\": 6, \"x\": 12, \"y\": 3},
              \"targets\": [
                {
                  \"expr\": \"up{job=\\\"cadvisor\\\", name=~\\\".*redis.*\\\"}\",
                  \"legendFormat\": \"Redis\",
                  \"refId\": \"A\"
                }
              ],
              \"fieldConfig\": {
                \"defaults\": {
                  \"color\": {\"mode\": \"thresholds\"},
                  \"mappings\": [
                    {\"options\": {\"0\": {\"text\": \"DOWN\", \"color\": \"red\"}}, \"type\": \"value\"},
                    {\"options\": {\"1\": {\"text\": \"UP\", \"color\": \"green\"}}, \"type\": \"value\"}
                  ],
                  \"thresholds\": {
                    \"steps\": [
                      {\"color\": \"red\", \"value\": null},
                      {\"color\": \"green\", \"value\": 1}
                    ]
                  }
                }
              },
              \"options\": {
                \"colorMode\": \"background\",
                \"graphMode\": \"none\",
                \"justifyMode\": \"center\"
              }
            },
            {
              \"id\": 5,
              \"title\": \"💾 Total Memory Usage\",
              \"type\": \"stat\",
              \"gridPos\": {\"h\": 4, \"w\": 6, \"x\": 18, \"y\": 3},
              \"targets\": [
                {
                  \"expr\": \"sum(container_memory_usage_bytes{name=~\\\"soloylibre_ultimate.*\\\"}) / 1024 / 1024\",
                  \"legendFormat\": \"Total MB\",
                  \"refId\": \"A\"
                }
              ],
              \"fieldConfig\": {
                \"defaults\": {
                  \"color\": {\"mode\": \"thresholds\"},
                  \"thresholds\": {
                    \"steps\": [
                      {\"color\": \"green\", \"value\": null},
                      {\"color\": \"yellow\", \"value\": 1000},
                      {\"color\": \"red\", \"value\": 2000}
                    ]
                  },
                  \"unit\": \"decbytes\"
                }
              },
              \"options\": {
                \"colorMode\": \"value\",
                \"graphMode\": \"area\",
                \"justifyMode\": \"center\"
              }
            },
            {
              \"id\": 6,
              \"title\": \"📈 WordPress CPU Usage Over Time\",
              \"type\": \"timeseries\",
              \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 0, \"y\": 7},
              \"targets\": [
                {
                  \"expr\": \"rate(container_cpu_usage_seconds_total{name=~\\\".*wordpress.*\\\"}[5m]) * 100\",
                  \"legendFormat\": \"WordPress CPU %\",
                  \"refId\": \"A\"
                },
                {
                  \"expr\": \"rate(container_cpu_usage_seconds_total{name=~\\\".*db.*\\\"}[5m]) * 100\",
                  \"legendFormat\": \"Database CPU %\",
                  \"refId\": \"B\"
                }
              ],
              \"fieldConfig\": {
                \"defaults\": {
                  \"color\": {\"mode\": \"palette-classic\"},
                  \"custom\": {
                    \"axisLabel\": \"CPU %\",
                    \"axisPlacement\": \"auto\",
                    \"drawStyle\": \"line\",
                    \"fillOpacity\": 10,
                    \"gradientMode\": \"none\",
                    \"lineWidth\": 2,
                    \"pointSize\": 5,
                    \"showPoints\": \"never\",
                    \"spanNulls\": false
                  },
                  \"unit\": \"percent\"
                }
              }
            },
            {
              \"id\": 7,
              \"title\": \"💾 Memory Usage Over Time\",
              \"type\": \"timeseries\",
              \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 12, \"y\": 7},
              \"targets\": [
                {
                  \"expr\": \"container_memory_usage_bytes{name=~\\\".*wordpress.*\\\"} / 1024 / 1024\",
                  \"legendFormat\": \"WordPress Memory MB\",
                  \"refId\": \"A\"
                },
                {
                  \"expr\": \"container_memory_usage_bytes{name=~\\\".*db.*\\\"} / 1024 / 1024\",
                  \"legendFormat\": \"Database Memory MB\",
                  \"refId\": \"B\"
                }
              ],
              \"fieldConfig\": {
                \"defaults\": {
                  \"color\": {\"mode\": \"palette-classic\"},
                  \"custom\": {
                    \"axisLabel\": \"Memory MB\",
                    \"axisPlacement\": \"auto\",
                    \"drawStyle\": \"line\",
                    \"fillOpacity\": 10,
                    \"gradientMode\": \"none\",
                    \"lineWidth\": 2,
                    \"pointSize\": 5,
                    \"showPoints\": \"never\",
                    \"spanNulls\": false
                  },
                  \"unit\": \"decbytes\"
                }
              }
            }
          ]
        }
      }
      DASHBOARD_EOF
      
      echo '✅ SoloYLibre Ultimate WordPress + Grafana auto-connection completed!' &&
      echo '🌐 WordPress: http://localhost:1051' &&
      echo '📊 Grafana: http://localhost:3000' &&
      echo '🔐 Username: soloylibre_ultimate' &&
      echo '👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)' &&
      echo '📧 Email: <EMAIL>' &&
      echo '📱 Phone: ************' &&
      echo '📍 Location: San Jose de Ocoa, Dom. Rep.' &&
      sleep 60
      "
    restart: "no"

volumes:
  wordpress_data:
    driver: local
  mysql_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local
  grafana_provisioning:
    driver: local
  prometheus_data:
    driver: local
  prometheus_config:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_complete_network
