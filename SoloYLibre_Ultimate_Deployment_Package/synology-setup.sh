#!/bin/bash

# SoloYLibre Ultimate - Synology RS3618xs Deployment Script
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>
# Phone: ************
# Location: San Jose de <PERSON>coa, Dom. Rep.
# Server: Synology RS3618xs | 56GB RAM | 36TB Storage

set -e

echo "🚀 SoloYLibre Ultimate - Synology Deployment Starting..."
echo "👨‍💻 Owner: <PERSON> (JoseTusabe)"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
echo "📍 Location: San Jose de Ocoa, Dom. Rep."
echo "🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB Storage"
echo "🎯 Platform: Multi-Application (Dating, E-commerce, Courses, Admin)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# Check if running on Synology
print_header "Checking Synology environment..."
if [ ! -d "/volume1" ]; then
    print_warning "Not running on Synology, creating local directories..."
    BASE_DIR="./synology-data"
else
    BASE_DIR="/volume1/docker/soloylibre"
    print_status "Synology environment detected ✅"
fi

# Create directory structure
print_header "Creating SoloYLibre directory structure..."
mkdir -p "$BASE_DIR"/{postgres,postgres-init,redis,minio,backend,frontend,websocket,nginx,ssl,logs/nginx,prometheus,prometheus-data,grafana,grafana-provisioning/{datasources,dashboards}}

print_status "Directory structure created ✅"

# Create PostgreSQL initialization script
print_header "Setting up PostgreSQL initialization..."
cat > "$BASE_DIR/postgres-init/01-init.sql" << 'EOF'
-- SoloYLibre Ultimate Database Initialization
-- Owner: Jose L Encarnacion (JoseTusabe)

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create main database schema
CREATE SCHEMA IF NOT EXISTS soloylibre;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Dating profiles table
CREATE TABLE IF NOT EXISTS dating_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    bio TEXT,
    age INTEGER,
    location VARCHAR(255),
    interests TEXT[],
    photos TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table for e-commerce
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    stock_quantity INTEGER DEFAULT 0,
    images TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Courses table
CREATE TABLE IF NOT EXISTS courses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor_id UUID REFERENCES users(id),
    price DECIMAL(10,2),
    duration_hours INTEGER,
    level VARCHAR(50),
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert super admin user
INSERT INTO users (email, username, password_hash, first_name, last_name, is_admin, is_verified)
VALUES (
    '<EMAIL>',
    'soloylibre_ultimate',
    '$2b$12$SoloYLibreUltimateHashForJoseTusabe',
    'Jose L',
    'Encarnacion',
    true,
    true
) ON CONFLICT (email) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_dating_profiles_user_id ON dating_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_courses_instructor ON courses(instructor_id);

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO soloylibre_ultimate;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO soloylibre_ultimate;
EOF

print_status "PostgreSQL initialization script created ✅"

# Create Nginx configuration
print_header "Setting up Nginx configuration..."
cat > "$BASE_DIR/nginx/default.conf" << 'EOF'
# SoloYLibre Ultimate Nginx Configuration
# Owner: Jose L Encarnacion (JoseTusabe)

upstream frontend {
    server soloylibre-ultimate-frontend:3000;
}

upstream backend {
    server soloylibre-ultimate-backend:8000;
}

upstream websocket {
    server soloylibre-ultimate-websocket:8001;
}

server {
    listen 80;
    server_name localhost soloylibre.com *.soloylibre.com;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Frontend routes
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API routes
    location /api/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket routes
    location /ws/ {
        proxy_pass http://websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Health check
    location /health {
        access_log off;
        return 200 "SoloYLibre Ultimate - Healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

print_status "Nginx configuration created ✅"

# Create Prometheus configuration
print_header "Setting up Prometheus configuration..."
cat > "$BASE_DIR/prometheus/prometheus.yml" << 'EOF'
# SoloYLibre Ultimate Prometheus Configuration
# Owner: Jose L Encarnacion (JoseTusabe)

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'soloylibre-ultimate-monitor'
    owner: 'josetusabe'
    environment: 'synology-production'

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'soloylibre-backend'
    static_configs:
      - targets: ['soloylibre-ultimate-backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'soloylibre-frontend'
    static_configs:
      - targets: ['soloylibre-ultimate-frontend:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgresql'
    static_configs:
      - targets: ['soloylibre-ultimate-postgresql:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['soloylibre-ultimate-redis:6379']
    scrape_interval: 30s

  - job_name: 'minio'
    static_configs:
      - targets: ['soloylibre-ultimate-minio:9000']
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['soloylibre-ultimate-nginx:80']
    scrape_interval: 30s
EOF

print_status "Prometheus configuration created ✅"

# Create Grafana datasource configuration
print_header "Setting up Grafana datasources..."
cat > "$BASE_DIR/grafana-provisioning/datasources/datasources.yml" << 'EOF'
apiVersion: 1
datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://soloylibre-ultimate-prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: '5s'
      queryTimeout: '60s'
      httpMethod: 'POST'
    version: 1

  - name: PostgreSQL
    type: postgres
    access: proxy
    url: soloylibre-ultimate-postgresql:5432
    database: soloylibre_db
    user: soloylibre_ultimate
    isDefault: false
    editable: true
    secureJsonData:
      password: 'PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?'
    version: 1

  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://soloylibre-ultimate-redis:6379
    isDefault: false
    editable: true
    secureJsonData:
      password: 'SoloYLibre2024Redis'
    version: 1
EOF

print_status "Grafana datasources configured ✅"

# Set proper permissions
print_header "Setting directory permissions..."
if [ -d "/volume1" ]; then
    chown -R 1000:1000 "$BASE_DIR" 2>/dev/null || true
    chmod -R 755 "$BASE_DIR"
    chmod 600 "$BASE_DIR/postgres-init/01-init.sql"
fi

print_status "Permissions set ✅"

print_success "🎉 SoloYLibre Ultimate setup completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Deploy using Portainer with the SYNOLOGY_SOLOYLIBRE_COMPLETE.yml file"
echo "2. Wait 5-10 minutes for all services to start"
echo "3. Access the platform at the URLs below"
echo ""
echo "🌐 Access URLs:"
echo "   🏠 Main Platform: http://localhost:80"
echo "   💕 Dating: http://localhost:80/dating"
echo "   🛒 E-commerce: http://localhost:80/shop"
echo "   🎓 Courses: http://localhost:80/courses"
echo "   🛡️ Admin: http://localhost:80/admin"
echo "   📚 API Docs: http://localhost:8000/docs"
echo "   📊 Grafana: http://localhost:3001"
echo "   📈 Prometheus: http://localhost:9090"
echo "   💾 MinIO: http://localhost:9001"
echo ""
echo "🔐 Login Credentials:"
echo "   Username: soloylibre_ultimate"
echo "   Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?"
echo "   Email: <EMAIL>"
echo ""
echo "👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
echo "📍 Location: San Jose de Ocoa, Dom. Rep."
echo "🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB Storage"
echo ""
print_success "Ready for deployment! 🚀"
