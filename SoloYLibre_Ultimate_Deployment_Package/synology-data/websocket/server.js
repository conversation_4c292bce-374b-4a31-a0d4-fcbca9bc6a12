// SoloYLibre Ultimate - WebSocket Server
// Owner: <PERSON> (JoseTusabe)
// Email: <EMAIL>
// Phone: ************
// Location: San Jose <PERSON>, Dom. Rep.

const WebSocket = require('ws');
const redis = require('redis');
const http = require('http');

// Configuration
const WS_PORT = process.env.WS_PORT || 8001;
const REDIS_URL = process.env.REDIS_URL || 'redis://:SoloYLibre2024Redis@redis:6379/1';

// Initialize Redis client
const redisClient = redis.createClient({ url: REDIS_URL });

redisClient.on('error', (err) => {
    console.error('❌ Redis connection error:', err);
});

redisClient.on('connect', () => {
    console.log('✅ Redis connected successfully');
});

// Connect to Redis
redisClient.connect().catch(console.error);

// Create HTTP server
const server = http.createServer();

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Store active connections
const connections = new Map();
const rooms = new Map();

// Utility functions
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

function broadcast(room, message, excludeId = null) {
    if (rooms.has(room)) {
        rooms.get(room).forEach(connectionId => {
            if (connectionId !== excludeId && connections.has(connectionId)) {
                const ws = connections.get(connectionId);
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify(message));
                }
            }
        });
    }
}

function joinRoom(connectionId, room) {
    if (!rooms.has(room)) {
        rooms.set(room, new Set());
    }
    rooms.get(room).add(connectionId);
    
    console.log(`📥 Connection ${connectionId} joined room: ${room}`);
}

function leaveRoom(connectionId, room) {
    if (rooms.has(room)) {
        rooms.get(room).delete(connectionId);
        if (rooms.get(room).size === 0) {
            rooms.delete(room);
        }
    }
    
    console.log(`📤 Connection ${connectionId} left room: ${room}`);
}

function leaveAllRooms(connectionId) {
    rooms.forEach((roomConnections, room) => {
        if (roomConnections.has(connectionId)) {
            leaveRoom(connectionId, room);
        }
    });
}

// WebSocket connection handler
wss.on('connection', (ws, req) => {
    const connectionId = generateId();
    connections.set(connectionId, ws);
    
    console.log(`🔗 New WebSocket connection: ${connectionId}`);
    console.log(`📊 Total connections: ${connections.size}`);
    
    // Send welcome message
    ws.send(JSON.stringify({
        type: 'welcome',
        connectionId: connectionId,
        message: 'Connected to SoloYLibre Ultimate WebSocket Server',
        owner: 'Jose L Encarnacion (JoseTusabe)',
        server: 'Synology RS3618xs | 56GB RAM | 36TB',
        timestamp: new Date().toISOString()
    }));

    // Handle incoming messages
    ws.on('message', async (data) => {
        try {
            const message = JSON.parse(data.toString());
            console.log(`📨 Message from ${connectionId}:`, message.type);

            switch (message.type) {
                case 'join_room':
                    joinRoom(connectionId, message.room);
                    ws.send(JSON.stringify({
                        type: 'room_joined',
                        room: message.room,
                        timestamp: new Date().toISOString()
                    }));
                    break;

                case 'leave_room':
                    leaveRoom(connectionId, message.room);
                    ws.send(JSON.stringify({
                        type: 'room_left',
                        room: message.room,
                        timestamp: new Date().toISOString()
                    }));
                    break;

                case 'dating_message':
                    // Handle dating chat messages
                    const chatMessage = {
                        type: 'dating_message',
                        from: message.from,
                        to: message.to,
                        content: message.content,
                        timestamp: new Date().toISOString(),
                        messageId: generateId()
                    };
                    
                    // Store message in Redis
                    await redisClient.lpush(
                        `chat:${message.from}:${message.to}`,
                        JSON.stringify(chatMessage)
                    );
                    
                    // Broadcast to room
                    broadcast(`chat:${message.from}:${message.to}`, chatMessage, connectionId);
                    break;

                case 'dating_swipe':
                    // Handle dating swipes
                    const swipeData = {
                        type: 'dating_swipe',
                        from: message.from,
                        to: message.to,
                        action: message.action, // 'like' or 'pass'
                        timestamp: new Date().toISOString()
                    };
                    
                    // Store swipe in Redis
                    await redisClient.set(
                        `swipe:${message.from}:${message.to}`,
                        JSON.stringify(swipeData),
                        { EX: 86400 } // Expire in 24 hours
                    );
                    
                    // Check for match
                    const reverseSwipe = await redisClient.get(`swipe:${message.to}:${message.from}`);
                    if (reverseSwipe && message.action === 'like') {
                        const reverseData = JSON.parse(reverseSwipe);
                        if (reverseData.action === 'like') {
                            // It's a match!
                            const matchData = {
                                type: 'dating_match',
                                user1: message.from,
                                user2: message.to,
                                timestamp: new Date().toISOString(),
                                matchId: generateId()
                            };
                            
                            // Store match in Redis
                            await redisClient.set(
                                `match:${matchData.matchId}`,
                                JSON.stringify(matchData)
                            );
                            
                            // Notify both users
                            broadcast(`user:${message.from}`, matchData);
                            broadcast(`user:${message.to}`, matchData);
                        }
                    }
                    break;

                case 'ecommerce_cart_update':
                    // Handle e-commerce cart updates
                    const cartUpdate = {
                        type: 'ecommerce_cart_update',
                        userId: message.userId,
                        productId: message.productId,
                        quantity: message.quantity,
                        action: message.action, // 'add', 'remove', 'update'
                        timestamp: new Date().toISOString()
                    };
                    
                    // Store cart in Redis
                    await redisClient.hset(
                        `cart:${message.userId}`,
                        message.productId,
                        message.quantity
                    );
                    
                    // Broadcast to user's sessions
                    broadcast(`user:${message.userId}`, cartUpdate, connectionId);
                    break;

                case 'course_progress':
                    // Handle course progress updates
                    const progressUpdate = {
                        type: 'course_progress',
                        userId: message.userId,
                        courseId: message.courseId,
                        lessonId: message.lessonId,
                        progress: message.progress,
                        timestamp: new Date().toISOString()
                    };
                    
                    // Store progress in Redis
                    await redisClient.hset(
                        `progress:${message.userId}:${message.courseId}`,
                        message.lessonId,
                        message.progress
                    );
                    
                    // Broadcast to user's sessions
                    broadcast(`user:${message.userId}`, progressUpdate, connectionId);
                    break;

                case 'admin_notification':
                    // Handle admin notifications
                    const notification = {
                        type: 'admin_notification',
                        title: message.title,
                        content: message.content,
                        severity: message.severity || 'info',
                        timestamp: new Date().toISOString(),
                        notificationId: generateId()
                    };
                    
                    // Store notification in Redis
                    await redisClient.lpush(
                        'admin:notifications',
                        JSON.stringify(notification)
                    );
                    
                    // Broadcast to admin room
                    broadcast('admin', notification);
                    break;

                case 'ping':
                    // Handle ping/pong for connection health
                    ws.send(JSON.stringify({
                        type: 'pong',
                        timestamp: new Date().toISOString()
                    }));
                    break;

                default:
                    console.log(`❓ Unknown message type: ${message.type}`);
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: 'Unknown message type',
                        timestamp: new Date().toISOString()
                    }));
            }
        } catch (error) {
            console.error('❌ Error processing message:', error);
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid message format',
                timestamp: new Date().toISOString()
            }));
        }
    });

    // Handle connection close
    ws.on('close', () => {
        console.log(`🔌 Connection closed: ${connectionId}`);
        leaveAllRooms(connectionId);
        connections.delete(connectionId);
        console.log(`📊 Total connections: ${connections.size}`);
    });

    // Handle connection error
    ws.on('error', (error) => {
        console.error(`❌ WebSocket error for ${connectionId}:`, error);
        leaveAllRooms(connectionId);
        connections.delete(connectionId);
    });
});

// Health check endpoint
server.on('request', (req, res) => {
    if (req.url === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'healthy',
            connections: connections.size,
            rooms: rooms.size,
            timestamp: new Date().toISOString(),
            owner: 'Jose L Encarnacion (JoseTusabe)',
            server: 'Synology RS3618xs | 56GB RAM | 36TB'
        }));
    } else {
        res.writeHead(404);
        res.end('Not Found');
    }
});

// Start server
server.listen(WS_PORT, () => {
    console.log('🚀 SoloYLibre Ultimate WebSocket Server Started');
    console.log('👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)');
    console.log('📧 Email: <EMAIL>');
    console.log('📱 Phone: ************');
    console.log('📍 Location: San Jose de Ocoa, Dom. Rep.');
    console.log('🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB');
    console.log(`🌐 WebSocket Server listening on port ${WS_PORT}`);
    console.log(`🔗 Health check: http://localhost:${WS_PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Shutting down WebSocket server...');
    wss.close(() => {
        redisClient.quit();
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🛑 Shutting down WebSocket server...');
    wss.close(() => {
        redisClient.quit();
        process.exit(0);
    });
});
