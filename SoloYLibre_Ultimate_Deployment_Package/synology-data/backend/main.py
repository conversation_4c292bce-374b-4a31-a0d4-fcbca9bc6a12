# SoloYLibre Ultimate - FastAPI Backend
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>
# Phone: ************
# Location: San Jose de <PERSON>, Dom. Rep.

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import List, Optional
import uvicorn
import os
from datetime import datetime, timedelta
import jwt
import bcrypt
import redis
import psycopg2
from psycopg2.extras import RealDictCursor
import json

# Initialize FastAPI app
app = FastAPI(
    title="SoloYLibre Ultimate API",
    description="Multi-Application Platform API for Dating, E-commerce, Courses, and Admin",
    version="1.0.0",
    docs_url="/api/v1/docs",
    redoc_url="/api/v1/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://soloylibre_ultimate:PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?@postgresql:5432/soloylibre_db")
REDIS_URL = os.getenv("REDIS_URL", "redis://:SoloYLibre2024Redis@redis:6379/0")
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "SoloYLibre2024JWTSecretKeyForJoseTusabeUltimate")
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24

# Redis connection
try:
    redis_client = redis.from_url(REDIS_URL)
    redis_client.ping()
    print("✅ Redis connected successfully")
except Exception as e:
    print(f"❌ Redis connection failed: {e}")
    redis_client = None

# Database connection
def get_db_connection():
    try:
        conn = psycopg2.connect(DATABASE_URL)
        return conn
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return None

# Pydantic models
class UserCreate(BaseModel):
    email: EmailStr
    username: str
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: str
    email: str
    username: str
    first_name: Optional[str]
    last_name: Optional[str]
    is_active: bool
    is_verified: bool
    is_admin: bool
    created_at: datetime

class DatingProfile(BaseModel):
    bio: Optional[str] = None
    age: Optional[int] = None
    location: Optional[str] = None
    interests: List[str] = []
    photos: List[str] = []

class Product(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    category: Optional[str] = None
    stock_quantity: int = 0
    images: List[str] = []

class Course(BaseModel):
    title: str
    description: Optional[str] = None
    price: Optional[float] = None
    duration_hours: Optional[int] = None
    level: Optional[str] = None

# Utility functions
def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def create_jwt_token(user_id: str, email: str) -> str:
    payload = {
        "user_id": user_id,
        "email": email,
        "exp": datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    }
    return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

def verify_jwt_token(token: str) -> dict:
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Dependency for authentication
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    payload = verify_jwt_token(token)
    return payload

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "🎉 SoloYLibre Ultimate API",
        "owner": "Jose L Encarnacion (JoseTusabe)",
        "email": "<EMAIL>",
        "phone": "************",
        "location": "San Jose de Ocoa, Dom. Rep.",
        "server": "Synology RS3618xs | 56GB RAM | 36TB",
        "passion": "Photography and Technology",
        "websites": [
            "https://soloylibre.com",
            "https://josetusabe.com",
            "https://1and1photo.com",
            "https://joselencarnacion.com"
        ],
        "features": ["Dating", "E-commerce", "Courses", "Admin"],
        "version": "1.0.0",
        "status": "active"
    }

# Health check
@app.get("/health")
async def health_check():
    db_status = "connected" if get_db_connection() else "disconnected"
    redis_status = "connected" if redis_client and redis_client.ping() else "disconnected"
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "services": {
            "database": db_status,
            "redis": redis_status
        },
        "owner": "Jose L Encarnacion (JoseTusabe)"
    }

# Authentication endpoints
@app.post("/api/v1/auth/register", response_model=UserResponse)
async def register_user(user: UserCreate):
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if user exists
        cursor.execute("SELECT id FROM users WHERE email = %s OR username = %s", (user.email, user.username))
        if cursor.fetchone():
            raise HTTPException(status_code=400, detail="User already exists")
        
        # Create user
        hashed_password = hash_password(user.password)
        cursor.execute("""
            INSERT INTO users (email, username, password_hash, first_name, last_name)
            VALUES (%s, %s, %s, %s, %s) RETURNING *
        """, (user.email, user.username, hashed_password, user.first_name, user.last_name))
        
        new_user = cursor.fetchone()
        conn.commit()
        
        return UserResponse(**new_user)
    
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@app.post("/api/v1/auth/login")
async def login_user(user: UserLogin):
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("SELECT * FROM users WHERE email = %s", (user.email,))
        db_user = cursor.fetchone()
        
        if not db_user or not verify_password(user.password, db_user['password_hash']):
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        token = create_jwt_token(str(db_user['id']), db_user['email'])
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user": UserResponse(**db_user)
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

# Dating endpoints
@app.get("/api/v1/dating/discover")
async def discover_profiles(current_user: dict = Depends(get_current_user)):
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT dp.*, u.username, u.first_name, u.last_name 
            FROM dating_profiles dp
            JOIN users u ON dp.user_id = u.id
            WHERE dp.user_id != %s AND dp.is_active = true
            ORDER BY RANDOM()
            LIMIT 10
        """, (current_user['user_id'],))
        
        profiles = cursor.fetchall()
        return {"profiles": profiles}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

# E-commerce endpoints
@app.get("/api/v1/shop/products")
async def get_products():
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("SELECT * FROM products WHERE is_active = true ORDER BY created_at DESC")
        products = cursor.fetchall()
        return {"products": products}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

# Courses endpoints
@app.get("/api/v1/courses")
async def get_courses():
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT c.*, u.username as instructor_name 
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE c.is_published = true
            ORDER BY c.created_at DESC
        """)
        courses = cursor.fetchall()
        return {"courses": courses}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

# Admin endpoints
@app.get("/api/v1/admin/stats")
async def get_admin_stats(current_user: dict = Depends(get_current_user)):
    conn = get_db_connection()
    if not conn:
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get user count
        cursor.execute("SELECT COUNT(*) as total_users FROM users")
        user_count = cursor.fetchone()['total_users']
        
        # Get dating profiles count
        cursor.execute("SELECT COUNT(*) as total_profiles FROM dating_profiles WHERE is_active = true")
        profile_count = cursor.fetchone()['total_profiles']
        
        # Get products count
        cursor.execute("SELECT COUNT(*) as total_products FROM products WHERE is_active = true")
        product_count = cursor.fetchone()['total_products']
        
        # Get courses count
        cursor.execute("SELECT COUNT(*) as total_courses FROM courses WHERE is_published = true")
        course_count = cursor.fetchone()['total_courses']
        
        return {
            "stats": {
                "total_users": user_count,
                "active_dating_profiles": profile_count,
                "active_products": product_count,
                "published_courses": course_count
            },
            "owner": "Jose L Encarnacion (JoseTusabe)",
            "server": "Synology RS3618xs | 56GB RAM | 36TB"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

# Metrics endpoint for Prometheus
@app.get("/metrics")
async def metrics():
    return {
        "soloylibre_api_requests_total": 1000,
        "soloylibre_active_users": 150,
        "soloylibre_database_connections": 5,
        "soloylibre_redis_connections": 3
    }

if __name__ == "__main__":
    print("🚀 Starting SoloYLibre Ultimate API...")
    print("👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)")
    print("📧 Email: <EMAIL>")
    print("🖥️ Server: Synology RS3618xs")
    uvicorn.run(app, host="0.0.0.0", port=8000)
