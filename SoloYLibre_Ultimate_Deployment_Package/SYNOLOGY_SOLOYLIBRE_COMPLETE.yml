version: '3.9'

services:
  # PostgreSQL Database - Optimized for Synology
  postgresql:
    image: postgres:15-alpine
    container_name: soloylibre_ultimate_postgresql_josetusabe
    hostname: soloylibre-ultimate-postgresql
    ports:
      - 5432:5432
    volumes:
      - /volume1/docker/soloylibre/postgres:/var/lib/postgresql/data:rw
      - /volume1/docker/soloylibre/postgres-init:/docker-entrypoint-initdb.d:ro
    environment:
      TZ: America/New_York
      POSTGRES_DB: soloylibre_db
      POSTGRES_USER: soloylibre_ultimate
      POSTGRES_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '1.0'
        reservations:
          memory: 2G
          cpus: '0.5'
    restart: unless-stopped

  # Redis Cache - Optimized for Synology
  redis:
    image: redis:7-alpine
    container_name: soloylibre_ultimate_redis_josetusabe
    hostname: soloylibre-ultimate-redis
    ports:
      - 6379:6379
    volumes:
      - /volume1/docker/soloylibre/redis:/data:rw
    command: redis-server --appendonly yes --requirepass "SoloYLibre2024Redis" --maxmemory 2gb --maxmemory-policy allkeys-lru
    environment:
      TZ: America/New_York
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '0.5'
        reservations:
          memory: 1G
          cpus: '0.25'
    restart: unless-stopped

  # MinIO Object Storage - Optimized for Synology
  minio:
    image: minio/minio:latest
    container_name: soloylibre_ultimate_minio_josetusabe
    hostname: soloylibre-ultimate-minio
    ports:
      - 9000:9000
      - 9001:9001
    volumes:
      - /volume1/docker/soloylibre/minio:/data:rw
    environment:
      TZ: America/New_York
      MINIO_ROOT_USER: soloylibre_ultimate
      MINIO_ROOT_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
    command: server /data --console-address ":9001"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    restart: unless-stopped

  # FastAPI Backend - Optimized for Synology
  backend:
    image: python:3.11-slim
    container_name: soloylibre_ultimate_backend_josetusabe
    hostname: soloylibre-ultimate-backend
    ports:
      - 8000:8000
    depends_on:
      - postgresql
      - redis
      - minio
    volumes:
      - /volume1/docker/soloylibre/backend:/app:rw
      - /volume1/docker/soloylibre/uploads:/app/uploads:rw
    environment:
      TZ: America/New_York
      DATABASE_URL: postgresql://soloylibre_ultimate:PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?@postgresql:5432/soloylibre_db
      REDIS_URL: redis://:SoloYLibre2024Redis@redis:6379/0
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: soloylibre_ultimate
      MINIO_SECRET_KEY: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      JWT_SECRET_KEY: SoloYLibre2024JWTSecretKeyForJoseTusabeUltimate
      ADMIN_EMAIL: <EMAIL>
      ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
    working_dir: /app
    command: >
      sh -c "
      echo '🚀 Setting up SoloYLibre Ultimate Backend...' &&
      pip install fastapi uvicorn sqlalchemy psycopg2-binary redis minio pydantic python-jose bcrypt python-multipart celery &&
      echo '✅ Dependencies installed' &&
      python -c 'print(\"Backend ready for SoloYLibre Ultimate - Jose L Encarnacion (JoseTusabe)\")' &&
      uvicorn main:app --host 0.0.0.0 --port 8000 --reload
      "
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    restart: unless-stopped

  # Next.js Frontend - Optimized for Synology
  frontend:
    image: node:18-alpine
    container_name: soloylibre_ultimate_frontend_josetusabe
    hostname: soloylibre-ultimate-frontend
    ports:
      - 3002:3000
    depends_on:
      - backend
    volumes:
      - /volume1/docker/soloylibre/frontend:/app:rw
    environment:
      TZ: America/New_York
      NEXT_PUBLIC_API_URL: http://localhost:8000/api/v1
      NEXT_PUBLIC_WS_URL: ws://localhost:8001
      NODE_ENV: development
    working_dir: /app
    command: >
      sh -c "
      echo '🎨 Setting up SoloYLibre Ultimate Frontend...' &&
      npm install next react react-dom typescript tailwindcss lucide-react &&
      echo '✅ Frontend dependencies installed' &&
      npm run dev
      "
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    restart: unless-stopped

  # WebSocket Server - Optimized for Synology
  websocket:
    image: node:18-alpine
    container_name: soloylibre_ultimate_websocket_josetusabe
    hostname: soloylibre-ultimate-websocket
    ports:
      - 8001:8001
    depends_on:
      - redis
    volumes:
      - /volume1/docker/soloylibre/websocket:/app:rw
    environment:
      TZ: America/New_York
      REDIS_URL: redis://:SoloYLibre2024Redis@redis:6379/1
      WS_PORT: 8001
    working_dir: /app
    command: >
      sh -c "
      echo '💬 Setting up SoloYLibre Ultimate WebSocket...' &&
      npm install ws redis socket.io &&
      echo '✅ WebSocket dependencies installed' &&
      node server.js
      "
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    restart: unless-stopped

  # Nginx Reverse Proxy - Optimized for Synology
  nginx:
    image: nginx:alpine
    container_name: soloylibre_ultimate_nginx_josetusabe
    hostname: soloylibre-ultimate-nginx
    ports:
      - 80:80
      - 443:443
    depends_on:
      - frontend
      - backend
    volumes:
      - /volume1/docker/soloylibre/nginx:/etc/nginx/conf.d:ro
      - /volume1/docker/soloylibre/ssl:/etc/ssl/certs:ro
      - /volume1/docker/soloylibre/logs/nginx:/var/log/nginx:rw
    environment:
      TZ: America/New_York
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    restart: unless-stopped

  # Prometheus Monitoring - Optimized for Synology
  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre_ultimate_prometheus_josetusabe
    hostname: soloylibre-ultimate-prometheus
    ports:
      - 9090:9090
    volumes:
      - /volume1/docker/soloylibre/prometheus:/etc/prometheus:ro
      - /volume1/docker/soloylibre/prometheus-data:/prometheus:rw
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    environment:
      TZ: America/New_York
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    restart: unless-stopped

  # Grafana Monitoring - Optimized for Synology
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    ports:
      - 3001:3000
    depends_on:
      - prometheus
    volumes:
      - /volume1/docker/soloylibre/grafana:/var/lib/grafana:rw
      - /volume1/docker/soloylibre/grafana-provisioning:/etc/grafana/provisioning:ro
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_DEFAULT_THEME: dark
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-worldmap-panel,redis-datasource
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/postgres
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/redis
  minio_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/minio
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/grafana

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_network
