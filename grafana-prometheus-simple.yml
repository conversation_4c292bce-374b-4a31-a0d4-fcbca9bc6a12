version: '3.9'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre_ultimate_prometheus_josetusabe
    hostname: soloylibre-ultimate-prometheus
    ports:
      - 9090:9090
    volumes:
      - prometheus_data:/prometheus:rw
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    environment:
      TZ: America/New_York
    restart: on-failure:5

  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    ports:
      - 3000:3000
    depends_on:
      - prometheus
    volumes:
      - grafana_data:/var/lib/grafana:rw
      - grafana_provisioning:/etc/grafana/provisioning:rw
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_SERVER_HTTP_PORT: 3000
      GF_SECURITY_DISABLE_GRAVATAR: true
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_ALLOW_ORG_CREATE: false
      GF_USERS_AUTO_ASSIGN_ORG: true
      GF_USERS_AUTO_ASSIGN_ORG_ROLE: Viewer
      GF_USERS_DEFAULT_THEME: dark
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
      GF_LOG_LEVEL: info
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
      GF_DATABASE_TYPE: sqlite3
      GF_ALERTING_ENABLED: true
      GF_UNIFIED_ALERTING_ENABLED: true
    labels:
      - "com.soloylibre.owner=josetusabe"
      - "com.soloylibre.service=grafana"
      - "com.soloylibre.environment=development"
    restart: on-failure:5

  node-exporter:
    image: prom/node-exporter:latest
    container_name: soloylibre_ultimate_node_exporter_josetusabe
    hostname: soloylibre-ultimate-node-exporter
    ports:
      - 9100:9100
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    environment:
      TZ: America/New_York
    restart: on-failure:5

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: soloylibre_ultimate_cadvisor_josetusabe
    hostname: soloylibre-ultimate-cadvisor
    ports:
      - 8080:8080
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    environment:
      TZ: America/New_York
    restart: on-failure:5

volumes:
  grafana_data:
    driver: local
  grafana_provisioning:
    driver: local
  prometheus_data:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_monitoring_network
