# SoloYLibre Ultimate Grafana Dashboard Provisioning
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>

apiVersion: 1

providers:
  - name: 'SoloYLibre Dashboards'
    orgId: 1
    folder: 'SoloYLibre'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards

  - name: 'Container Monitoring'
    orgId: 1
    folder: 'Containers'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/containers

  - name: 'System Monitoring'
    orgId: 1
    folder: 'System'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/system

  - name: 'Photography Workflows'
    orgId: 1
    folder: 'Photography'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/photography
