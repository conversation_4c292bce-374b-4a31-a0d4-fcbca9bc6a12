# SoloYLibre Ultimate Grafana Datasources
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>

apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://soloylibre-prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}
    version: 1

  - name: SoloYLibre-Prometheus
    type: prometheus
    access: proxy
    url: http://soloylibre-prometheus:9090
    isDefault: false
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "GET"
      customQueryParameters: "owner=josetusabe&environment=development"
    secureJsonData: {}
    version: 1
    uid: soloylibre-prometheus

  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://soloylibre-redis:6379
    isDefault: false
    editable: true
    jsonData:
      client: "standalone"
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    secureJsonData:
      password: "SoloYLibre2024Redis"
    version: 1
    uid: soloylibre-redis
