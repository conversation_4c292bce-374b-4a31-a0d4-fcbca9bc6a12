# SoloYLibre Ultimate Prometheus Configuration
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'soloylibre-monitor'
    owner: 'josetusabe'
    environment: 'development'

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # Node Exporter - System metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['soloylibre-node-exporter:9100']
    scrape_interval: 5s
    metrics_path: /metrics

  # cAdvisor - Container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['soloylibre-cadvisor:8080']
    scrape_interval: 5s
    metrics_path: /metrics

  # Portainer metrics (if available)
  - job_name: 'portainer'
    static_configs:
      - targets: ['soloyl<PERSON>re-portainer:9000']
    scrape_interval: 30s
    metrics_path: /api/status
    scheme: http

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['soloylibre-grafana:3000']
    scrape_interval: 30s
    metrics_path: /metrics
    scheme: http

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['soloylibre-redis:6379']
    scrape_interval: 30s

  # Docker daemon metrics (if enabled)
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s
    metrics_path: /metrics

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Remote write configuration (for external storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"

# Remote read configuration (for external storage)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint"
