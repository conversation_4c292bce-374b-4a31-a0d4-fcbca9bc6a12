apiVersion: 1
datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://soloylibre-ultimate-prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: '5s'
      queryTimeout: '60s'
      httpMethod: 'POST'
    version: 1

  - name: PostgreSQL
    type: postgres
    access: proxy
    url: soloylibre-ultimate-postgresql:5432
    database: soloylibre_db
    user: soloylibre_ultimate
    isDefault: false
    editable: true
    secureJsonData:
      password: 'PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?'
    version: 1

  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://soloylibre-ultimate-redis:6379
    isDefault: false
    editable: true
    secureJsonData:
      password: 'SoloYLibre2024Redis'
    version: 1
