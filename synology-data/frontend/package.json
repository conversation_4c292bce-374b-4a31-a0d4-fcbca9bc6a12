{"name": "soloylibre-ultimate-frontend", "version": "1.0.0", "description": "SoloYLibre Ultimate Frontend - Multi-Application Platform", "author": "<PERSON> (JoseTusabe) <<EMAIL>>", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.0", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.2.2", "@types/node": "20.8.0", "@types/react": "18.2.25", "@types/react-dom": "18.2.11", "tailwindcss": "3.3.5", "autoprefixer": "10.4.16", "postcss": "8.4.31", "lucide-react": "0.288.0", "axios": "1.5.1", "js-cookie": "3.0.5", "@types/js-cookie": "3.0.4", "framer-motion": "10.16.4", "react-hook-form": "7.47.0", "react-hot-toast": "2.4.1", "socket.io-client": "4.7.2", "swiper": "10.3.1", "react-intersection-observer": "9.5.2"}, "devDependencies": {"eslint": "8.51.0", "eslint-config-next": "14.0.0"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "dating", "ecommerce", "courses", "admin", "nextjs", "react", "typescript", "tailwindcss", "jose<PERSON><PERSON>"], "repository": {"type": "git", "url": "https://github.com/josetusabe/soloylibre-ultimate"}, "homepage": "https://soloylibre.com", "bugs": {"url": "https://github.com/josetusabe/soloylibre-ultimate/issues", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}