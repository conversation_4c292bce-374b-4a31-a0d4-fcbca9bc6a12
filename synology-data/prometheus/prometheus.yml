# SoloYLibre Ultimate Prometheus Configuration
# Owner: <PERSON> (JoseTusabe)

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'soloylibre-ultimate-monitor'
    owner: 'josetusabe'
    environment: 'synology-production'

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'soloylibre-backend'
    static_configs:
      - targets: ['soloylibre-ultimate-backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'soloylibre-frontend'
    static_configs:
      - targets: ['soloylibre-ultimate-frontend:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgresql'
    static_configs:
      - targets: ['soloylibre-ultimate-postgresql:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['soloylibre-ultimate-redis:6379']
    scrape_interval: 30s

  - job_name: 'minio'
    static_configs:
      - targets: ['soloylibre-ultimate-minio:9000']
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['soloylibre-ultimate-nginx:80']
    scrape_interval: 30s
