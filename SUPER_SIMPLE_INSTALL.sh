#!/bin/bash

# SoloYLibre Ultimate - Super Simple Installation
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>
# Phone: ************
# Location: San Jose de <PERSON>, Dom. Rep.

clear
echo "🚀 SoloYLibre Ultimate - Super Simple Installation"
echo "👨‍💻 Owner: <PERSON> (JoseTusabe)"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
echo "📍 Location: San Jose de Ocoa, Dom. Rep."
echo "🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB"
echo ""

# Clean up any existing containers
echo "🧹 Cleaning up any existing containers..."
docker-compose -f SIMPLE_COMPLETE_INSTALL.yml down --remove-orphans 2>/dev/null || true

# Start the installation
echo "🚀 Starting SoloYLibre Ultimate installation..."
echo "⏳ This will take about 10-15 minutes..."
echo ""

# Deploy the stack
docker-compose -f SIMPLE_COMPLETE_INSTALL.yml up -d

echo ""
echo "✅ Installation started successfully!"
echo ""
echo "⏳ Please wait 10-15 minutes for everything to be ready..."
echo ""
echo "🌐 Your websites will be available at:"
echo "   WordPress: http://localhost:1052"
echo "   WordPress Admin: http://localhost:1052/wp-admin"
echo "   Grafana: http://localhost:3001"
echo "   phpMyAdmin: http://localhost:2051"
echo ""
echo "🔐 Login credentials for all services:"
echo "   Username: soloylibre_ultimate"
echo "   Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?"
echo "   Email: <EMAIL>"
echo ""
echo "📊 To check installation progress:"
echo "   docker-compose -f SIMPLE_COMPLETE_INSTALL.yml logs -f auto-setup"
echo ""
echo "👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)"
echo "🎯 Perfect for photography business and JEYKO AI development!"
echo ""
echo "✅ Installation script completed!"
