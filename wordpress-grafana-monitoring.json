{"dashboard": {"id": null, "title": "SoloYLibre Ultimate - WordPress Monitoring Dashboard", "description": "Complete WordPress monitoring for Jose L Encarnacion (JoseTusabe)", "tags": ["wordpress", "<PERSON><PERSON><PERSON><PERSON>", "jose<PERSON><PERSON>", "monitoring"], "timezone": "America/New_York", "editable": true, "graphTooltip": 1, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "refresh": "30s", "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "WordPress Site", "tooltip": "Open WordPress Site", "type": "link", "url": "http://localhost:1051"}, {"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "WordPress Admin", "tooltip": "Open WordPress Admin", "type": "link", "url": "http://localhost:1051/wp-admin"}, {"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "phpMyAdmin", "tooltip": "Open phpMyAdmin", "type": "link", "url": "http://localhost:2051"}, {"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "SoloYLibre.com", "tooltip": "Visit SoloYLibre Website", "type": "link", "url": "https://soloylibre.com"}], "panels": [{"id": 1, "title": "SoloYLibre WordPress Overview", "type": "text", "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}, "options": {"mode": "markdown", "content": "# 🎉 SoloYLibre Ultimate WordPress Monitoring\n\n**Owner:** <PERSON> (JoseTusabe) | **Email:** <EMAIL> | **Phone:** ************ | **Location:** San Jose <PERSON>, Dom. Rep.\n\n**Server:** Synology RS3618xs | 56GB RAM | 36TB | **Environment:** JEYKO AI Development | **Passion:** Photography and Technology 📸💻"}}, {"id": 2, "title": "WordPress Container Status", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 4}, "targets": [{"expr": "up{job=\"cadvisor\", container_label_com_docker_compose_service=\"wordpress\"}", "legendFormat": "WordPress", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "Down"}}, "type": "value"}, {"options": {"1": {"text": "Up"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 3, "title": "Database Container Status", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 4}, "targets": [{"expr": "up{job=\"cadvisor\", container_label_com_docker_compose_service=\"db\"}", "legendFormat": "MariaDB", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "Down"}}, "type": "value"}, {"options": {"1": {"text": "Up"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 4, "title": "Redis Container Status", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 4}, "targets": [{"expr": "up{job=\"cadvisor\", container_label_com_docker_compose_service=\"redis\"}", "legendFormat": "Redis", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "Down"}}, "type": "value"}, {"options": {"1": {"text": "Up"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 5, "title": "phpMyAdmin Status", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 4}, "targets": [{"expr": "up{job=\"cadvisor\", container_label_com_docker_compose_service=\"phpmyadmin\"}", "legendFormat": "phpMyAdmin", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "Down"}}, "type": "value"}, {"options": {"1": {"text": "Up"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 6, "title": "WordPress CPU Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "targets": [{"expr": "rate(container_cpu_usage_seconds_total{name=~\".*wordpress.*\"}[5m]) * 100", "legendFormat": "WordPress CPU %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "CPU %", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}, "unit": "percent"}}}, {"id": 7, "title": "WordPress Memory Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "targets": [{"expr": "container_memory_usage_bytes{name=~\".*wordpress.*\"} / 1024 / 1024", "legendFormat": "WordPress Memory MB", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Memory MB", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}, "unit": "decbytes"}}}, {"id": 8, "title": "Database Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "targets": [{"expr": "rate(container_cpu_usage_seconds_total{name=~\".*db.*\"}[5m]) * 100", "legendFormat": "Database CPU %", "refId": "A"}, {"expr": "container_memory_usage_bytes{name=~\".*db.*\"} / 1024 / 1024", "legendFormat": "Database Memory MB", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}}}}, {"id": 9, "title": "Redis Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "targets": [{"expr": "rate(container_cpu_usage_seconds_total{name=~\".*redis.*\"}[5m]) * 100", "legendFormat": "Redis CPU %", "refId": "A"}, {"expr": "container_memory_usage_bytes{name=~\".*redis.*\"} / 1024 / 1024", "legendFormat": "Redis Memory MB", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}}}}, {"id": 10, "title": "Network Traffic", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "targets": [{"expr": "rate(container_network_receive_bytes_total{name=~\".*wordpress.*\"}[5m])", "legendFormat": "WordPress RX", "refId": "A"}, {"expr": "rate(container_network_transmit_bytes_total{name=~\".*wordpress.*\"}[5m])", "legendFormat": "WordPress TX", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bytes/sec", "axisPlacement": "auto", "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "lineWidth": 2, "pointSize": 5, "showPoints": "never", "spanNulls": false}, "unit": "binBps"}}}]}}