#!/bin/bash

# SoloYLibre Ultimate Monitoring Stack Setup
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>
# Phone: ************
# Location: San Jose de <PERSON>coa, Dom. Rep.

set -e

echo "🚀 Setting up SoloYLibre Ultimate Monitoring Stack..."
echo "👨‍💻 Owner: <PERSON> (JoseTusabe)"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
echo "📍 Location: San Jose de Ocoa, Dom. Rep."
echo "🌐 Websites: soloylibre.com | josetusabe.com | 1and1photo.com | joselencarnacion.com"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# Check if Docker is running
print_header "Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi
print_status "Docker is running ✅"

# Create necessary directories
print_header "Creating directory structure..."
mkdir -p grafana/dashboards/containers
mkdir -p grafana/dashboards/system
mkdir -p grafana/dashboards/photography
mkdir -p prometheus
print_status "Directories created ✅"

# Stop any existing containers
print_header "Cleaning up existing containers..."
docker-compose down 2>/dev/null || true
docker stop $(docker ps -q --filter "name=soloylibre-") 2>/dev/null || true
docker rm $(docker ps -aq --filter "name=soloylibre-") 2>/dev/null || true
print_status "Cleanup completed ✅"

# Start the monitoring stack
print_header "Starting SoloYLibre Monitoring Stack..."
docker-compose up -d

# Wait for services to start
print_status "Waiting for services to initialize..."
sleep 30

# Check service status
print_header "Checking service status..."

services=("soloylibre-portainer" "soloylibre-grafana" "soloylibre-prometheus" "soloylibre-node-exporter" "soloylibre-cadvisor" "soloylibre-redis")

for service in "${services[@]}"; do
    if docker ps | grep -q "$service"; then
        print_status "$service is running ✅"
    else
        print_error "$service failed to start ❌"
    fi
done

# Test connectivity
print_header "Testing service connectivity..."

# Test Portainer
if curl -s http://localhost:9000 > /dev/null; then
    print_status "Portainer is accessible ✅"
else
    print_warning "Portainer may still be starting..."
fi

# Test Grafana
if curl -s http://localhost:3000 > /dev/null; then
    print_status "Grafana is accessible ✅"
else
    print_warning "Grafana may still be starting..."
fi

# Test Prometheus
if curl -s http://localhost:9090 > /dev/null; then
    print_status "Prometheus is accessible ✅"
else
    print_warning "Prometheus may still be starting..."
fi

echo ""
print_success "🎉 SoloYLibre Ultimate Monitoring Stack Setup Complete!"
echo ""
echo "🌐 Access URLs:"
echo "   📊 Portainer:   http://localhost:9000"
echo "   📈 Grafana:     http://localhost:3000"
echo "   🔍 Prometheus:  http://localhost:9090"
echo "   📊 cAdvisor:    http://localhost:8080"
echo "   💾 Redis:       localhost:6379"
echo ""
echo "🔐 Login Credentials:"
echo "   👤 Username: soloylibre_ultimate"
echo "   🔑 Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?"
echo ""
echo "📊 Container Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=soloylibre-"
echo ""
echo "🎯 Next Steps:"
echo "   1. Access Portainer at http://localhost:9000"
echo "   2. Create admin account with the credentials above"
echo "   3. Access Grafana at http://localhost:3000"
echo "   4. Explore pre-configured dashboards"
echo "   5. Monitor your containers and system metrics"
echo ""
echo "📞 Support Contact:"
echo "   📧 Email: <EMAIL>"
echo "   📱 Phone: ************"
echo "   📍 Location: San Jose de Ocoa, Dom. Rep."
echo ""
echo "🌐 Visit our websites:"
echo "   🔗 https://soloylibre.com"
echo "   🔗 https://josetusabe.com"
echo "   📸 https://1and1photo.com"
echo "   👤 https://joselencarnacion.com"
echo ""
print_success "Developed with ❤️ by Jose L Encarnacion (JoseTusabe)"
print_success "Passionate about Photography and Technology"
print_success "Server: Synology RS3618xs | 56GB RAM | 36TB Storage"
