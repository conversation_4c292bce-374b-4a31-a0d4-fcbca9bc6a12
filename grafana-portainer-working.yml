version: '3.9'

services:
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    ports:
      - 3000:3000
    volumes:
      - grafana_data:/var/lib/grafana:rw
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_SERVER_HTTP_PORT: 3000
      GF_SECURITY_DISABLE_GRAVATAR: true
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_ALLOW_ORG_CREATE: false
      GF_USERS_AUTO_ASSIGN_ORG: true
      GF_USERS_AUTO_ASSIGN_ORG_ROLE: Viewer
      GF_USERS_DEFAULT_THEME: dark
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
      GF_LOG_LEVEL: info
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
      GF_DATABASE_TYPE: sqlite3
      GF_ALERTING_ENABLED: true
      GF_UNIFIED_ALERTING_ENABLED: true
    labels:
      - "com.soloylibre.owner=josetusabe"
      - "com.soloylibre.service=grafana"
      - "com.soloylibre.environment=development"
    restart: on-failure:5

volumes:
  grafana_data:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_grafana_network
