version: '3.9'

services:
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    healthcheck:
      test: curl -f http://localhost:3000/ || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    ports:
      - 3000:3000
    depends_on:
      prometheus:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - grafana_data:/var/lib/grafana:rw
      - grafana_config:/etc/grafana:rw
      - grafana_logs:/var/log/grafana:rw
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_SERVER_DOMAIN: grafana.soloylibre.com
      GF_SERVER_ROOT_URL: https://grafana.soloylibre.com
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel,redis-datasource,prometheus
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
      GF_SECURITY_DISABLE_GRAVATAR: true
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_ALLOW_ORG_CREATE: false
      GF_USERS_AUTO_ASSIGN_ORG: true
      GF_USERS_AUTO_ASSIGN_ORG_ROLE: Viewer
      GF_LOG_LEVEL: info
      GF_PATHS_DATA: /var/lib/grafana
      GF_PATHS_LOGS: /var/log/grafana
      GF_PATHS_PLUGINS: /var/lib/grafana/plugins
      GF_PATHS_PROVISIONING: /etc/grafana/provisioning
    restart: on-failure:5

  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre_ultimate_prometheus_josetusabe
    hostname: soloylibre-ultimate-prometheus
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9090/api/v1/status/config || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    ports:
      - 9090:9090
    depends_on:
      node-exporter:
        condition: service_healthy
      cadvisor:
        condition: service_healthy
    volumes:
      - prometheus_data:/prometheus:rw
      - prometheus_config:/etc/prometheus:rw
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    environment:
      TZ: America/New_York
    restart: on-failure:5

  node-exporter:
    image: prom/node-exporter:latest
    container_name: soloylibre_ultimate_node_exporter_josetusabe
    hostname: soloylibre-ultimate-node-exporter
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9100/metrics || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 9100:9100
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
      - '--web.listen-address=0.0.0.0:9100'
    environment:
      TZ: America/New_York
    restart: on-failure:5

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: soloylibre_ultimate_cadvisor_josetusabe
    hostname: soloylibre-ultimate-cadvisor
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/metrics || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 8080:8080
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    environment:
      TZ: America/New_York
    restart: on-failure:5

  redis:
    image: redis:7-alpine
    container_name: soloylibre_ultimate_redis_josetusabe
    hostname: soloylibre-ultimate-redis
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data:rw
      - redis_config:/usr/local/etc/redis:rw
    command: redis-server --appendonly yes --requirepass "SoloYLibre2024Redis" --maxmemory 256mb --maxmemory-policy allkeys-lru
    environment:
      TZ: America/New_York
      REDIS_PASSWORD: SoloYLibre2024Redis
    restart: on-failure:5

  grafana-init:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_init_josetusabe
    hostname: soloylibre-ultimate-grafana-init
    depends_on:
      grafana:
        condition: service_healthy
      prometheus:
        condition: service_healthy
    volumes:
      - grafana_config:/etc/grafana:rw
    environment:
      TZ: America/New_York
    command: >
      sh -c "
      echo 'Initializing Grafana configuration for SoloYLibre Ultimate...' &&
      mkdir -p /etc/grafana/provisioning/datasources &&
      mkdir -p /etc/grafana/provisioning/dashboards &&
      cat > /etc/grafana/provisioning/datasources/prometheus.yml << 'EOF'
      apiVersion: 1
      datasources:
        - name: Prometheus
          type: prometheus
          access: proxy
          url: http://soloylibre-ultimate-prometheus:9090
          isDefault: true
          editable: true
          jsonData:
            timeInterval: '5s'
            queryTimeout: '60s'
            httpMethod: 'POST'
          version: 1
        - name: Redis
          type: redis-datasource
          access: proxy
          url: redis://soloylibre-ultimate-redis:6379
          isDefault: false
          editable: true
          jsonData:
            client: 'standalone'
            poolSize: 5
            timeout: 10
          secureJsonData:
            password: 'SoloYLibre2024Redis'
          version: 1
      EOF
      &&
      cat > /etc/grafana/provisioning/dashboards/dashboard.yml << 'EOF'
      apiVersion: 1
      providers:
        - name: 'SoloYLibre Dashboards'
          orgId: 1
          folder: 'SoloYLibre'
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards
      EOF
      &&
      echo 'SoloYLibre Ultimate Grafana configuration completed!' &&
      sleep 10
      "
    restart: "no"

volumes:
  grafana_data:
    driver: local
  grafana_config:
    driver: local
  grafana_logs:
    driver: local
  prometheus_data:
    driver: local
  prometheus_config:
    driver: local
  redis_data:
    driver: local
  redis_config:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_monitoring_network
