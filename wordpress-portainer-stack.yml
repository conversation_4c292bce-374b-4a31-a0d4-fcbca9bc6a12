version: '3.9'

services:
  wordpress:
    image: wordpress:php8.2
    container_name: soloylibre_ultimate_wordpress_josetusabe
    hostname: soloylibre-ultimate-wordpress
    healthcheck:
      test: curl -f http://localhost:80/ || exit 1
    ports:
      - 1051:80
    depends_on:
      db:
        condition: service_started
      redis:
        condition: service_healthy
      phpmyadmin:
        condition: service_healthy
    volumes:
      - wordpress_data:/var/www/html:rw
      - wordpress_uploads:/var/www/html/wp-content/uploads:rw
    environment:
      TZ: America/New_York
      WORDPRESS_DB_HOST: soloylibre_ultimate_wordpress_db_josetusabe
      WORDPRESS_DB_USER: soloylibre_ultimate
      WORDPRESS_DB_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      WORDPRESS_DB_NAME: soloylibre_ultimate_db
      WORDPRESS_TABLE_PREFIX: wp_soloylibre_
      WORDPRESS_DEBUG: 0
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_REDIS_HOST', 'soloylibre_ultimate_redis_josetusabe');
        define('WP_REDIS_PORT', 6379);
        define('WP_REDIS_PASSWORD', 'SoloYLibre2024Redis');
        define('WP_REDIS_DATABASE', 0);
        define('WP_CACHE', true);
        define('FORCE_SSL_ADMIN', false);
        define('WP_MEMORY_LIMIT', '512M');
        define('WP_MAX_MEMORY_LIMIT', '1024M');
        define('AUTOMATIC_UPDATER_DISABLED', false);
        define('WP_AUTO_UPDATE_CORE', true);
    restart: on-failure:5

  db:
    image: mariadb:11.3-jammy
    container_name: soloylibre_ultimate_wordpress_db_josetusabe
    hostname: soloylibre-ultimate-wordpress-db
    security_opt:
      - no-new-privileges:true
    healthcheck:
      test: ["CMD-SHELL", "mysqladmin ping -h localhost -u root -p$$MYSQL_ROOT_PASSWORD"]
    environment:
      TZ: America/New_York
      MYSQL_DATABASE: soloylibre_ultimate_db
      MYSQL_USER: soloylibre_ultimate
      MYSQL_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      MYSQL_ROOT_PASSWORD: SoloYLibre2024RootDB!JoseTusabe
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql:rw
      - mysql_config:/etc/mysql/conf.d:rw
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=200
      --innodb_buffer_pool_size=512M
      --innodb_log_file_size=128M
      --query_cache_size=64M
      --query_cache_type=1
      --slow_query_log=1
      --long_query_time=2
    restart: on-failure:5

  redis:
    image: redis:7-alpine
    container_name: soloylibre_ultimate_redis_josetusabe
    hostname: soloylibre-ultimate-redis
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping || exit 1"]
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data:rw
    command: redis-server --appendonly yes --requirepass "SoloYLibre2024Redis" --maxmemory 256mb --maxmemory-policy allkeys-lru
    environment:
      TZ: America/New_York
    restart: on-failure:5

  phpmyadmin:
    image: phpmyadmin:latest
    container_name: soloylibre_ultimate_phpmyadmin_josetusabe
    hostname: soloylibre-ultimate-phpmyadmin
    healthcheck:
      test: curl -f http://localhost:80/ || exit 1
    ports:
      - 2051:80
    depends_on:
      db:
        condition: service_healthy
    environment:
      TZ: America/New_York
      PMA_HOST: soloylibre_ultimate_wordpress_db_josetusabe
      PMA_PORT: 3306
      PMA_USER: soloylibre_ultimate
      PMA_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      MYSQL_ROOT_PASSWORD: SoloYLibre2024RootDB!JoseTusabe
      PMA_ARBITRARY: 1
      PMA_ABSOLUTE_URI: http://localhost:2051/
      UPLOAD_LIMIT: 1024M
      MEMORY_LIMIT: 512M
      MAX_EXECUTION_TIME: 600
    restart: on-failure:5

  wp-cli:
    image: wordpress:cli-php8.2
    container_name: soloylibre_ultimate_wpcli_josetusabe
    hostname: soloylibre-ultimate-wpcli
    depends_on:
      wordpress:
        condition: service_healthy
      db:
        condition: service_healthy
    volumes:
      - wordpress_data:/var/www/html:rw
    environment:
      TZ: America/New_York
      WORDPRESS_DB_HOST: soloylibre_ultimate_wordpress_db_josetusabe
      WORDPRESS_DB_USER: soloylibre_ultimate
      WORDPRESS_DB_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      WORDPRESS_DB_NAME: soloylibre_ultimate_db
    command: >
      sh -c "
      echo 'Waiting for WordPress to be ready...' &&
      sleep 60 &&
      echo 'Installing WordPress...' &&
      wp core install 
        --url='http://localhost:1051' 
        --title='SoloYLibre Ultimate - Jose L Encarnacion' 
        --admin_user='soloylibre_ultimate' 
        --admin_password='PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?' 
        --admin_email='<EMAIL>' 
        --allow-root &&
      echo 'Installing essential plugins...' &&
      wp plugin install redis-cache --activate --allow-root &&
      wp plugin install wp-super-cache --activate --allow-root &&
      wp plugin install contact-form-7 --activate --allow-root &&
      wp plugin install yoast-seo --activate --allow-root &&
      wp plugin install elementor --activate --allow-root &&
      echo 'Configuring Redis cache...' &&
      wp redis enable --allow-root &&
      echo 'Setting up theme...' &&
      wp theme install twentytwentyfour --activate --allow-root &&
      echo 'Creating sample content...' &&
      wp post create --post_type=page --post_title='About Jose L Encarnacion' --post_content='<h1>Welcome to SoloYLibre Ultimate</h1><p><strong>Owner:</strong> Jose L Encarnacion (JoseTusabe)</p><p><strong>Email:</strong> <EMAIL></p><p><strong>Phone:</strong> ************</p><p><strong>Location:</strong> San Jose de Ocoa, Dom. Rep.</p><p><strong>Passion:</strong> Photography and Technology</p><h2>Websites</h2><ul><li><a href=\"https://soloylibre.com\">SoloYLibre</a></li><li><a href=\"https://josetusabe.com\">JoseTusabe</a></li><li><a href=\"https://1and1photo.com\">1and1Photo</a></li><li><a href=\"https://joselencarnacion.com\">Jose L Encarnacion</a></li></ul><h2>Server Specifications</h2><p><strong>Model:</strong> Synology RS3618xs</p><p><strong>Memory:</strong> 56GB RAM</p><p><strong>Storage:</strong> 36TB</p><p><strong>Environment:</strong> JEYKO AI Development</p>' --post_status=publish --allow-root &&
      echo 'WordPress setup completed for SoloYLibre Ultimate!' &&
      echo 'Access: http://localhost:1051' &&
      echo 'Admin: http://localhost:1051/wp-admin' &&
      echo 'phpMyAdmin: http://localhost:2051' &&
      echo 'Username: soloylibre_ultimate' &&
      echo 'Owner: Jose L Encarnacion (JoseTusabe)' &&
      tail -f /dev/null
      "
    restart: "no"

volumes:
  wordpress_data:
    driver: local
  wordpress_uploads:
    driver: local
  mysql_data:
    driver: local
  mysql_config:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_wordpress_network
