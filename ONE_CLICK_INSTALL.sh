#!/bin/bash

# SoloYLibre Ultimate - One-Click Complete Installation
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>
# Phone: ************
# Location: San Jose <PERSON>, Dom. Rep.
# Server: Synology RS3618xs | 56GB RAM | 36TB Storage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo ""
    echo -e "${CYAN}================================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Main installation header
clear
print_header "🚀 SoloYLibre Ultimate - One-Click Installation"
echo -e "${PURPLE}👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)${NC}"
echo -e "${PURPLE}📧 Email: <EMAIL>${NC}"
echo -e "${PURPLE}📱 Phone: ************${NC}"
echo -e "${PURPLE}📍 Location: San Jose de Ocoa, Dom. Rep.${NC}"
echo -e "${PURPLE}🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB${NC}"
echo -e "${PURPLE}🎯 Platform: Complete WordPress + Grafana + Monitoring${NC}"
echo ""

# Check if Docker is installed
print_info "Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_success "Docker and Docker Compose are installed"

# Check if Docker is running
print_info "Checking Docker service..."
if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker service."
    exit 1
fi

print_success "Docker service is running"

# Stop any existing containers with the same names
print_info "Cleaning up any existing containers..."
docker-compose -f AUTOMATIC_COMPLETE_INSTALL.yml down --remove-orphans 2>/dev/null || true
docker system prune -f 2>/dev/null || true

print_success "Cleanup completed"

# Pull all required images first to avoid timeouts
print_header "📥 Downloading Required Images (This may take 5-10 minutes)"

images=(
    "postgres:15-alpine"
    "redis:7-alpine"
    "wordpress:php8.2-apache"
    "phpmyadmin:latest"
    "grafana/grafana:latest"
    "prom/prometheus:latest"
    "prom/node-exporter:latest"
    "gcr.io/cadvisor/cadvisor:latest"
    "alpine:latest"
)

for image in "${images[@]}"; do
    print_info "Pulling $image..."
    docker pull "$image"
    print_success "Downloaded $image"
done

print_success "All images downloaded successfully!"

# Deploy the stack
print_header "🚀 Deploying SoloYLibre Ultimate Platform"
print_info "Starting all services..."

docker-compose -f AUTOMATIC_COMPLETE_INSTALL.yml up -d

print_success "All services started!"

# Wait for services to be ready
print_header "⏳ Waiting for Services to Initialize (5-10 minutes)"

print_info "Waiting for database to be ready..."
timeout=300
counter=0
while ! docker exec soloylibre_database_josetusabe pg_isready -U soloylibre_ultimate -d soloylibre_db &>/dev/null; do
    if [ $counter -ge $timeout ]; then
        print_error "Database failed to start within timeout"
        exit 1
    fi
    sleep 5
    counter=$((counter + 5))
    echo -n "."
done
print_success "Database is ready!"

print_info "Waiting for Redis to be ready..."
counter=0
while ! docker exec soloylibre_cache_josetusabe redis-cli ping &>/dev/null; do
    if [ $counter -ge $timeout ]; then
        print_error "Redis failed to start within timeout"
        exit 1
    fi
    sleep 5
    counter=$((counter + 5))
    echo -n "."
done
print_success "Redis is ready!"

print_info "Waiting for WordPress to be ready..."
counter=0
while ! curl -s http://localhost:1051 &>/dev/null; do
    if [ $counter -ge $timeout ]; then
        print_error "WordPress failed to start within timeout"
        exit 1
    fi
    sleep 10
    counter=$((counter + 10))
    echo -n "."
done
print_success "WordPress is ready!"

print_info "Waiting for Grafana to be ready..."
counter=0
while ! curl -s http://localhost:3000 &>/dev/null; do
    if [ $counter -ge $timeout ]; then
        print_error "Grafana failed to start within timeout"
        exit 1
    fi
    sleep 10
    counter=$((counter + 10))
    echo -n "."
done
print_success "Grafana is ready!"

# Wait for auto-setup to complete
print_info "Waiting for automatic configuration to complete..."
sleep 180  # Give auto-setup container time to complete

# Check service status
print_header "📊 Service Status Check"

services=(
    "soloylibre_database_josetusabe:Database"
    "soloylibre_cache_josetusabe:Redis Cache"
    "soloylibre_wordpress_josetusabe:WordPress"
    "soloylibre_phpmyadmin_josetusabe:phpMyAdmin"
    "soloylibre_grafana_josetusabe:Grafana"
    "soloylibre_prometheus_josetusabe:Prometheus"
    "soloylibre_node_exporter_josetusabe:Node Exporter"
    "soloylibre_cadvisor_josetusabe:cAdvisor"
)

all_running=true
for service in "${services[@]}"; do
    container_name=$(echo $service | cut -d: -f1)
    service_name=$(echo $service | cut -d: -f2)
    
    if docker ps --format "table {{.Names}}" | grep -q "$container_name"; then
        print_success "$service_name is running"
    else
        print_error "$service_name is not running"
        all_running=false
    fi
done

if [ "$all_running" = true ]; then
    print_success "All services are running successfully!"
else
    print_warning "Some services may need more time to start"
fi

# Final success message
print_header "🎉 INSTALLATION COMPLETE!"

echo -e "${GREEN}✅ SoloYLibre Ultimate has been successfully installed!${NC}"
echo ""
echo -e "${CYAN}🌐 ACCESS INFORMATION:${NC}"
echo -e "${YELLOW}   🏠 WordPress Site:     http://localhost:1051${NC}"
echo -e "${YELLOW}   🛡️ WordPress Admin:    http://localhost:1051/wp-admin${NC}"
echo -e "${YELLOW}   📊 Grafana Dashboard:  http://localhost:3000${NC}"
echo -e "${YELLOW}   🗄️ phpMyAdmin:         http://localhost:2051${NC}"
echo -e "${YELLOW}   📈 Prometheus:         http://localhost:9091${NC}"
echo -e "${YELLOW}   📊 cAdvisor:           http://localhost:8081${NC}"
echo ""
echo -e "${CYAN}🔐 LOGIN CREDENTIALS (All Services):${NC}"
echo -e "${GREEN}   Username: soloylibre_ultimate${NC}"
echo -e "${GREEN}   Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?${NC}"
echo -e "${GREEN}   Email:    <EMAIL>${NC}"
echo ""
echo -e "${CYAN}📊 DATABASE ACCESS:${NC}"
echo -e "${GREEN}   Host:     localhost:5433${NC}"
echo -e "${GREEN}   Database: soloylibre_db${NC}"
echo -e "${GREEN}   Username: soloylibre_ultimate${NC}"
echo -e "${GREEN}   Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?${NC}"
echo ""
echo -e "${CYAN}🔴 REDIS ACCESS:${NC}"
echo -e "${GREEN}   Host:     localhost:6380${NC}"
echo -e "${GREEN}   Password: SoloYLibre2024Redis${NC}"
echo ""
echo -e "${PURPLE}👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)${NC}"
echo -e "${PURPLE}📧 Email: <EMAIL>${NC}"
echo -e "${PURPLE}📱 Phone: ************${NC}"
echo -e "${PURPLE}📍 Location: San Jose de Ocoa, Dom. Rep.${NC}"
echo -e "${PURPLE}🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB${NC}"
echo ""
echo -e "${CYAN}🎯 FEATURES INSTALLED:${NC}"
echo -e "${GREEN}   ✅ Complete WordPress Platform${NC}"
echo -e "${GREEN}   ✅ Grafana Monitoring Dashboard${NC}"
echo -e "${GREEN}   ✅ Prometheus Metrics Collection${NC}"
echo -e "${GREEN}   ✅ Database Management (phpMyAdmin)${NC}"
echo -e "${GREEN}   ✅ Redis Cache for Performance${NC}"
echo -e "${GREEN}   ✅ System Monitoring (Node Exporter)${NC}"
echo -e "${GREEN}   ✅ Container Monitoring (cAdvisor)${NC}"
echo -e "${GREEN}   ✅ All Services Auto-Connected${NC}"
echo ""
echo -e "${YELLOW}🚀 Your platform is ready for:${NC}"
echo -e "${GREEN}   📸 Photography Business${NC}"
echo -e "${GREEN}   🤖 JEYKO AI Development${NC}"
echo -e "${GREEN}   🛒 E-commerce (WooCommerce installed)${NC}"
echo -e "${GREEN}   📊 Performance Monitoring${NC}"
echo ""
echo -e "${CYAN}📝 NEXT STEPS:${NC}"
echo -e "${YELLOW}   1. Visit http://localhost:1051 to see your WordPress site${NC}"
echo -e "${YELLOW}   2. Login to WordPress admin with the credentials above${NC}"
echo -e "${YELLOW}   3. Check Grafana dashboard for monitoring${NC}"
echo -e "${YELLOW}   4. Customize your site for your photography business${NC}"
echo ""
print_success "Installation completed successfully! Enjoy your SoloYLibre Ultimate platform!"
