version: '3.9'

services:
  portainer:
    image: portainer/portainer-ce:latest
    container_name: soloylibre_ultimate_portainer_josetusabe
    hostname: soloylibre-ultimate-portainer
    healthcheck:
      test: curl -f http://localhost:9000/ || exit 1
    ports:
      - 9000:9000
      - 9443:9443
    depends_on:
      portainer-agent:
        condition: service_started
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data:rw
    environment:
      TZ: America/New_York
      PORTAINER_ADMIN_USERNAME: soloylibre_ultimate
      PORTAINER_ADMIN_EMAIL: <EMAIL>
    restart: on-failure:5

  portainer-agent:
    image: portainer/agent:latest
    container_name: soloylibre_ultimate_portainer_agent_josetusabe
    hostname: soloylibre-ultimate-portainer-agent
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9001/ || exit 1"]
    ports:
      - 9001:9001
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:rw
      - /var/lib/docker/volumes:/var/lib/docker/volumes:rw
    environment:
      TZ: America/New_York
      AGENT_CLUSTER_ADDR: soloylibre-ultimate-portainer-agent
      LOG_LEVEL: INFO
    restart: on-failure:5

  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    healthcheck:
      test: curl -f http://localhost:3000/ || exit 1
    ports:
      - 3000:3000
    depends_on:
      prometheus:
        condition: service_started
      redis:
        condition: service_healthy
    volumes:
      - grafana_data:/var/lib/grafana:rw
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_SERVER_DOMAIN: grafana.soloylibre.com
      GF_SERVER_ROOT_URL: https://grafana.soloylibre.com
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
    restart: on-failure:5

  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre_ultimate_prometheus_josetusabe
    hostname: soloylibre-ultimate-prometheus
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9090/ || exit 1"]
    ports:
      - 9090:9090
    depends_on:
      node-exporter:
        condition: service_started
      cadvisor:
        condition: service_started
    volumes:
      - prometheus_data:/prometheus:rw
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    environment:
      TZ: America/New_York
    restart: on-failure:5

  node-exporter:
    image: prom/node-exporter:latest
    container_name: soloylibre_ultimate_node_exporter_josetusabe
    hostname: soloylibre-ultimate-node-exporter
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9100/ || exit 1"]
    ports:
      - 9100:9100
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    environment:
      TZ: America/New_York
    restart: on-failure:5

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: soloylibre_ultimate_cadvisor_josetusabe
    hostname: soloylibre-ultimate-cadvisor
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1"]
    ports:
      - 8080:8080
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    environment:
      TZ: America/New_York
    restart: on-failure:5

  redis:
    image: redis:7-alpine
    container_name: soloylibre_ultimate_redis_josetusabe
    hostname: soloylibre-ultimate-redis
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping || exit 1"]
    ports:
      - 6382:6379
    volumes:
      - redis_data:/data:rw
    command: redis-server --appendonly yes --requirepass "SoloYLibre2024Redis"
    environment:
      TZ: America/New_York
    restart: on-failure:5

volumes:
  portainer_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_monitoring_network
