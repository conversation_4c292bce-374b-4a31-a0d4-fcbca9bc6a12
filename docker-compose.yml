services:
  # Portainer - Container Management
  portainer:
    image: portainer/portainer-ce:latest
    container_name: soloylibre-portainer
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9443:9443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
    environment:
      - PORTAINER_ADMIN_USERNAME=soloylibre_ultimate
      - PORTAINER_ADMIN_PASSWORD=$$2y$$10$$SoloYLibreUltimate2024Hash
    networks:
      - monitoring
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer.rule=Host(`portainer.soloylibre.com`)"
      - "traefik.http.services.portainer.loadbalancer.server.port=9000"

  # Portainer Agent
  portainer-agent:
    image: portainer/agent:latest
    container_name: soloylibre-portainer-agent
    restart: unless-stopped
    ports:
      - "9001:9001"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/volumes:/var/lib/docker/volumes
    networks:
      - monitoring
    environment:
      - AGENT_CLUSTER_ADDR=soloylibre-portainer-agent

  # Grafana - Monitoring Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=soloylibre_ultimate
      - GF_SECURITY_ADMIN_PASSWORD=PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      - GF_SECURITY_ADMIN_EMAIL=<EMAIL>
      - GF_SERVER_DOMAIN=grafana.soloylibre.com
      - GF_SERVER_ROOT_URL=https://grafana.soloylibre.com
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel
      - GF_FEATURE_TOGGLES_ENABLE=publicDashboards
    networks:
      - monitoring
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.soloylibre.com`)"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  # Prometheus - Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  # Node Exporter - System Metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: soloylibre-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring

  # cAdvisor - Container Metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: soloylibre-cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    networks:
      - monitoring

  # Redis - Cache for Grafana
  redis:
    image: redis:7-alpine
    container_name: soloylibre-redis
    restart: unless-stopped
    ports:
      - "6382:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass "SoloYLibre2024Redis"
    networks:
      - monitoring

volumes:
  portainer_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local
  redis_data:
    driver: local

networks:
  monitoring:
    driver: bridge
    name: soloylibre-monitoring
