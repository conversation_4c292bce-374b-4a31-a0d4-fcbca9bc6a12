{"soloylibre_wordpress_monitoring": {"owner": {"name": "Jose <PERSON> Encarnacion (JoseTusabe)", "email": "<EMAIL>", "phone": "************", "location": "San Jose <PERSON>, Dom. Rep.", "passion": "Photography and Technology", "server": "Synology RS3618xs | 56GB RAM | 36TB"}, "websites": ["https://soloylibre.com", "https://josetusabe.com", "https://1and1photo.com", "https://joselencarnacion.com"], "monitoring_configuration": {"wordpress_endpoints": {"site": "http://localhost:1051", "admin": "http://localhost:1051/wp-admin", "api": "http://localhost:1051/wp-json/wp/v2", "health": "http://localhost:1051/wp-admin/admin-ajax.php?action=heartbeat"}, "database_endpoints": {"phpmyadmin": "http://localhost:2051", "mysql_host": "soloylibre_ultimate_wordpress_db_jose<PERSON><PERSON>", "mysql_port": 3306, "database_name": "soloylibre_ultimate_db"}, "cache_endpoints": {"redis_host": "soloylibre_ultimate_redis_jose<PERSON>abe", "redis_port": 6379, "redis_password": "SoloYLibre2024Redis"}, "monitoring_endpoints": {"grafana": "http://localhost:3000", "prometheus": "http://localhost:9090", "cadvisor": "http://localhost:8080", "node_exporter": "http://localhost:9100"}}, "grafana_alerts": [{"name": "WordPress Container Down", "condition": "up{job=\"cadvisor\", container_label_com_docker_compose_service=\"wordpress\"} == 0", "severity": "critical", "message": "WordPress container is down for SoloYLibre Ultimate", "notification_channels": ["email", "webhook"]}, {"name": "Database Container Down", "condition": "up{job=\"cadvisor\", container_label_com_docker_compose_service=\"db\"} == 0", "severity": "critical", "message": "MariaDB database container is down for SoloYLibre Ultimate", "notification_channels": ["email", "webhook"]}, {"name": "High WordPress CPU Usage", "condition": "rate(container_cpu_usage_seconds_total{name=~\".*wordpress.*\"}[5m]) * 100 > 80", "severity": "warning", "message": "WordPress CPU usage is above 80% for SoloYLibre Ultimate", "notification_channels": ["email"]}, {"name": "High WordPress Memory Usage", "condition": "container_memory_usage_bytes{name=~\".*wordpress.*\"} / container_spec_memory_limit_bytes{name=~\".*wordpress.*\"} * 100 > 85", "severity": "warning", "message": "WordPress memory usage is above 85% for SoloYLibre Ultimate", "notification_channels": ["email"]}, {"name": "<PERSON><PERSON>", "condition": "up{job=\"cadvisor\", container_label_com_docker_compose_service=\"redis\"} == 0", "severity": "warning", "message": "Redis cache is down for SoloYLibre Ultimate WordPress", "notification_channels": ["email"]}], "prometheus_scrape_configs": [{"job_name": "wordpress-containers", "static_configs": [{"targets": ["cadvisor:8080"]}], "scrape_interval": "15s", "metrics_path": "/metrics", "params": {"container": ["soloylibre_ultimate_wordpress_jose<PERSON>abe", "soloylibre_ultimate_wordpress_db_jose<PERSON><PERSON>", "soloylibre_ultimate_redis_jose<PERSON>abe", "soloyl<PERSON>re_ultimate_php<PERSON><PERSON><PERSON>_jose<PERSON><PERSON>"]}}, {"job_name": "wordpress-health", "static_configs": [{"targets": ["localhost:1051"]}], "scrape_interval": "30s", "metrics_path": "/wp-admin/admin-ajax.php", "params": {"action": ["heartbeat"]}}], "dashboard_panels": [{"title": "WordPress Site Status", "type": "stat", "query": "up{job=\"wordpress-health\"}", "thresholds": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, {"title": "Active WordPress Users", "type": "stat", "query": "wordpress_active_users_total", "description": "Number of currently active users on WordPress site"}, {"title": "WordPress Page Load Time", "type": "timeseries", "query": "wordpress_page_load_duration_seconds", "description": "Average page load time for WordPress site"}, {"title": "Database Connections", "type": "timeseries", "query": "mysql_global_status_threads_connected", "description": "Number of active database connections"}, {"title": "<PERSON><PERSON> Hit Rate", "type": "stat", "query": "redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) * 100", "description": "Redis cache hit rate percentage"}, {"title": "WordPress Plugin Status", "type": "table", "query": "wordpress_plugin_status", "description": "Status of installed WordPress plugins"}], "notification_settings": {"email": {"enabled": true, "smtp_host": "smtp.gmail.com", "smtp_port": 587, "from_address": "<EMAIL>", "from_name": "SoloYLibre WordPress Monitoring", "to_addresses": ["<EMAIL>"]}, "webhook": {"enabled": true, "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "channel": "#soloylibre-alerts", "username": "Grafana-SoloYLibre"}}, "backup_monitoring": {"wordpress_files": "/var/www/html", "database_backup": "mysqldump soloylibre_ultimate_db", "backup_schedule": "0 2 * * *", "backup_retention": "30 days", "backup_location": "/volume1/web_packages/docker/backups/wordpress"}, "security_monitoring": {"failed_login_attempts": "wp_login_failed_total", "brute_force_threshold": 10, "suspicious_activity": "wp_suspicious_requests_total", "file_integrity": "wp_core_files_modified_total"}, "performance_thresholds": {"cpu_warning": 70, "cpu_critical": 85, "memory_warning": 80, "memory_critical": 90, "disk_warning": 85, "disk_critical": 95, "response_time_warning": 2000, "response_time_critical": 5000}, "custom_metrics": {"wordpress_posts_total": "Total number of published posts", "wordpress_pages_total": "Total number of published pages", "wordpress_comments_total": "Total number of approved comments", "wordpress_users_total": "Total number of registered users", "wordpress_themes_active": "Currently active WordPress theme", "wordpress_plugins_active": "Number of active plugins", "wordpress_updates_available": "Number of available updates"}}}