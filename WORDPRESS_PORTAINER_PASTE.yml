version: '3.9'

services:
  wordpress:
    image: wordpress:php8.2
    container_name: soloylibre_ultimate_wordpress_josetusabe
    hostname: soloylibre-ultimate-wordpress
    ports:
      - 1051:80
    depends_on:
      - db
      - redis
    volumes:
      - wordpress_data:/var/www/html:rw
    environment:
      TZ: America/New_York
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: soloylibre_ultimate
      WORDPRESS_DB_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      WORDPRESS_DB_NAME: soloylibre_ultimate_db
      WORDPRESS_TABLE_PREFIX: wp_soloylibre_
      WORDPRESS_DEBUG: 0
    restart: unless-stopped

  db:
    image: mariadb:11.3-jammy
    container_name: soloylibre_ultimate_wordpress_db_josetusabe
    hostname: soloylibre-ultimate-wordpress-db
    security_opt:
      - no-new-privileges:true
    environment:
      TZ: America/New_York
      MYSQL_DATABASE: soloylibre_ultimate_db
      MYSQL_USER: soloylibre_ultimate
      MYSQL_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      MYSQL_ROOT_PASSWORD: SoloYLibre2024RootDB!JoseTusabe
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql:rw
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=200
      --innodb_buffer_pool_size=512M
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: soloylibre_ultimate_redis_josetusabe
    hostname: soloylibre-ultimate-redis
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data:rw
    command: redis-server --appendonly yes --requirepass "SoloYLibre2024Redis"
    environment:
      TZ: America/New_York
    restart: unless-stopped

  phpmyadmin:
    image: phpmyadmin:latest
    container_name: soloylibre_ultimate_phpmyadmin_josetusabe
    hostname: soloylibre-ultimate-phpmyadmin
    ports:
      - 2051:80
    depends_on:
      - db
    environment:
      TZ: America/New_York
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: soloylibre_ultimate
      PMA_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      MYSQL_ROOT_PASSWORD: SoloYLibre2024RootDB!JoseTusabe
      PMA_ARBITRARY: 1
      UPLOAD_LIMIT: 1024M
    restart: unless-stopped

volumes:
  wordpress_data:
    driver: local
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_wordpress_network
