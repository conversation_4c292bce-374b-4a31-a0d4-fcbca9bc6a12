version: '3.9'

services:
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    ports:
      - 3000:3000
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_DEFAULT_THEME: dark
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-worldmap-panel
    restart: unless-stopped

volumes:
  grafana_data:
