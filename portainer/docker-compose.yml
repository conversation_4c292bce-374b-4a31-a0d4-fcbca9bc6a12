version: '3.8'

services:
  portainer:
    image: portainer/portainer-ce:latest
    container_name: soloylibre-portainer
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
      - ./config:/config
      - ./ssl:/ssl:ro
    ports:
      - "9000:9000"
      - "9443:9443"
    environment:
      - PORTAINER_ADMIN_PASSWORD_FILE=/config/admin_password
      - PORTAINER_LOGO=https://soloylibre.com/logo.png
    command: >
      --admin-password-file /config/admin_password
      --logo "https://soloylibre.com/logo.png"
      --templates "https://raw.githubusercontent.com/portainer/templates/master/templates-2.0.json"
    networks:
      - soloylibre-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer.rule=Host(`portainer.soloylibre.com`)"
      - "traefik.http.routers.portainer.entrypoints=websecure"
      - "traefik.http.routers.portainer.tls.certresolver=letsencrypt"
      - "traefik.http.services.portainer.loadbalancer.server.port=9000"
      - "traefik.http.routers.portainer-secure.rule=Host(`portainer.soloylibre.com`)"
      - "traefik.http.routers.portainer-secure.entrypoints=websecure"
      - "traefik.http.routers.portainer-secure.tls=true"

  portainer-agent:
    image: portainer/agent:latest
    container_name: soloylibre-portainer-agent
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/volumes:/var/lib/docker/volumes
    ports:
      - "9001:9001"
    networks:
      - soloylibre-network
    environment:
      - AGENT_CLUSTER_ADDR=soloylibre-portainer-agent
      - LOG_LEVEL=INFO

volumes:
  portainer_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data

  soloylibre-home:
    image: nginx:alpine
    container_name: soloylibre-home
    restart: unless-stopped
    ports:
      - "8090:80"
    volumes:
      - ./custom:/usr/share/nginx/html:ro
      - ./custom/nginx.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - soloylibre-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.soloylibre-home.rule=Host(`home.soloylibre.com`)"
      - "traefik.http.routers.soloylibre-home.entrypoints=websecure"
      - "traefik.http.routers.soloylibre-home.tls.certresolver=letsencrypt"
      - "traefik.http.services.soloylibre-home.loadbalancer.server.port=80"

networks:
  soloylibre-network:
    external: true
    name: soloylibre-network
