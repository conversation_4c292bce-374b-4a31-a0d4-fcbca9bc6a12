// SoloYLibre Ultimate Custom JavaScript
// Owner: <PERSON> (JoseTusabe)

(function() {
    'use strict';
    
    // Add custom branding
    function addCustomBranding() {
        const navbar = document.querySelector('.navbar-brand');
        if (navbar && !navbar.querySelector('.soloylibre-badge')) {
            const badge = document.createElement('span');
            badge.className = 'jeyko-ai-badge soloylibre-badge';
            badge.textContent = 'JEYKO AI';
            navbar.appendChild(badge);
        }
    }
    
    // Add owner information footer
    function addOwnerFooter() {
        if (!document.querySelector('.soloylibre-footer')) {
            const footer = document.createElement('div');
            footer.className = 'soloylibre-footer';
            footer.innerHTML = `
                <div style="background: #2c3e50; color: #ecf0f1; padding: 1rem; text-align: center; margin-top: 2rem;">
                    <p style="margin: 0; font-weight: 600;">SoloYLibre Ultimate - Container Management</p>
                    <p style="margin: 0; font-size: 0.9rem; opacity: 0.8;">
                        Developed with ❤️ by <PERSON> (JoseTusabe)<br>
                        📧 <EMAIL> | 📱 718-713-5500 | 📍 San Jose de Ocoa, Dom. Rep.
                    </p>
                    <p style="margin: 0; font-size: 0.8rem; opacity: 0.6;">
                        🌐 <a href="https://soloylibre.com" target="_blank" style="color: #3498db;">SoloYLibre</a> | 
                        <a href="https://josetusabe.com" target="_blank" style="color: #3498db;">JoseTusabe</a> | 
                        <a href="https://1and1photo.com" target="_blank" style="color: #3498db;">1and1Photo</a> | 
                        <a href="https://joselencarnacion.com" target="_blank" style="color: #3498db;">Jose L Encarnacion</a>
                    </p>
                </div>
            `;
            document.body.appendChild(footer);
        }
    }
    
    // Add custom dashboard stats
    function addDashboardStats() {
        const mainContent = document.querySelector('.main-content');
        if (mainContent && !document.querySelector('.soloylibre-stats')) {
            const statsDiv = document.createElement('div');
            statsDiv.className = 'soloylibre-stats';
            statsDiv.innerHTML = `
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                    <h5>🚀 SoloYLibre Ultimate Dashboard</h5>
                    <p style="margin: 0; opacity: 0.9;">JEYKO AI Development Environment | Server: Synology RS3618xs (56GB RAM, 36TB)</p>
                </div>
            `;
            mainContent.insertBefore(statsDiv, mainContent.firstChild);
        }
    }
    
    // Initialize customizations
    function initCustomizations() {
        addCustomBranding();
        addOwnerFooter();
        addDashboardStats();
    }
    
    // Run customizations when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCustomizations);
    } else {
        initCustomizations();
    }
    
    // Re-run customizations on navigation (for SPA)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                setTimeout(initCustomizations, 500);
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
})();
