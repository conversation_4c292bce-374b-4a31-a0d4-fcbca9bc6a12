<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Ultimate - Container Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --dark-color: #34495e;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .hero-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            margin: 2rem 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            margin-bottom: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .card-custom {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .card-custom:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .quick-action-btn {
            width: 100%;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 10px;
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, var(--secondary-color), #5dade2);
            color: white;
        }
        
        .btn-success-custom {
            background: linear-gradient(45deg, var(--success-color), #58d68d);
            color: white;
        }
        
        .btn-warning-custom {
            background: linear-gradient(45deg, var(--warning-color), #f7dc6f);
            color: white;
        }
        
        .btn-danger-custom {
            background: linear-gradient(45deg, var(--accent-color), #ec7063);
            color: white;
        }
        
        .owner-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .website-links a {
            color: var(--secondary-color);
            text-decoration: none;
            margin: 0 1rem;
            font-weight: 500;
        }
        
        .website-links a:hover {
            color: var(--accent-color);
        }
        
        .server-specs {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-section">
                    <div class="logo-container">
                        <div class="logo">
                            <i class="fas fa-cube"></i>
                        </div>
                        <h1 class="text-white mb-0">SoloYLibre Ultimate</h1>
                        <p class="text-white-50 mb-0">Container Management Dashboard</p>
                        <p class="text-white-50 small">JEYKO AI Development Environment</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Statistics Cards -->
            <div class="col-md-3">
                <div class="card card-custom stat-card">
                    <div class="stat-number text-primary" id="containerCount">--</div>
                    <h5>Active Containers</h5>
                    <i class="fas fa-box fa-2x text-primary"></i>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-custom stat-card">
                    <div class="stat-number text-success" id="imageCount">--</div>
                    <h5>Docker Images</h5>
                    <i class="fas fa-layer-group fa-2x text-success"></i>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-custom stat-card">
                    <div class="stat-number text-warning" id="volumeCount">--</div>
                    <h5>Volumes</h5>
                    <i class="fas fa-database fa-2x text-warning"></i>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-custom stat-card">
                    <div class="stat-number text-info" id="networkCount">--</div>
                    <h5>Networks</h5>
                    <i class="fas fa-network-wired fa-2x text-info"></i>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Quick Actions -->
            <div class="col-md-6">
                <div class="card card-custom">
                    <div class="card-header">
                        <h5><i class="fas fa-rocket"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary-custom quick-action-btn" onclick="window.open('/#!/1/docker/containers', '_blank')">
                            <i class="fas fa-list"></i> View All Containers
                        </button>
                        <button class="btn btn-success-custom quick-action-btn" onclick="window.open('/#!/1/docker/images', '_blank')">
                            <i class="fas fa-plus-circle"></i> Deploy New Container
                        </button>
                        <button class="btn btn-warning-custom quick-action-btn" onclick="window.open('/#!/1/docker/stacks', '_blank')">
                            <i class="fas fa-layer-group"></i> Manage Stacks
                        </button>
                        <button class="btn btn-danger-custom quick-action-btn" onclick="window.open('/#!/1/docker/volumes', '_blank')">
                            <i class="fas fa-database"></i> Volume Management
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="col-md-6">
                <div class="card card-custom">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Docker Engine</span>
                            <span class="badge bg-success">Running</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Portainer Agent</span>
                            <span class="badge bg-success">Connected</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>SoloYLibre Network</span>
                            <span class="badge bg-success">Active</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Last Backup</span>
                            <span class="badge bg-info">Today</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Owner Information -->
        <div class="row">
            <div class="col-12">
                <div class="owner-info">
                    <div class="row">
                        <div class="col-md-8">
                            <h5><i class="fas fa-user-circle"></i> Owner Information</h5>
                            <p><strong>Name:</strong> Jose L Encarnacion (JoseTusabe)</p>
                            <p><strong>Email:</strong> <EMAIL></p>
                            <p><strong>Phone:</strong> ************</p>
                            <p><strong>Location:</strong> San Jose de Ocoa, Dom. Rep.</p>
                            <p><strong>Passion:</strong> Photography and Technology</p>
                            
                            <div class="website-links">
                                <strong>Websites:</strong>
                                <a href="https://soloylibre.com" target="_blank">SoloYLibre</a>
                                <a href="https://josetusabe.com" target="_blank">JoseTusabe</a>
                                <a href="https://1and1photo.com" target="_blank">1and1Photo</a>
                                <a href="https://joselencarnacion.com" target="_blank">Jose L Encarnacion</a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="server-specs">
                                <h6><i class="fas fa-server"></i> Server Specifications</h6>
                                <p><strong>Model:</strong> Synology RS3618xs</p>
                                <p><strong>Memory:</strong> 56GB RAM</p>
                                <p><strong>Storage:</strong> 36TB</p>
                                <p><strong>Environment:</strong> JEYKO AI Development</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simulate loading Docker stats (in real implementation, these would come from Portainer API)
        setTimeout(() => {
            document.getElementById('containerCount').textContent = '24';
            document.getElementById('imageCount').textContent = '18';
            document.getElementById('volumeCount').textContent = '12';
            document.getElementById('networkCount').textContent = '6';
        }, 1000);
        
        // Add some animation
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card-custom');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
