/* SoloYLibre Ultimate Custom Theme for Portainer */
/* Owner: <PERSON> (JoseTusabe) */
/* Email: <EMAIL> */

:root {
    --soloylibre-primary: #667eea;
    --soloylibre-secondary: #764ba2;
    --soloylibre-accent: #e74c3c;
    --soloylibre-success: #27ae60;
    --soloylibre-warning: #f39c12;
    --soloylibre-info: #3498db;
    --soloylibre-dark: #2c3e50;
    --soloylibre-light: #ecf0f1;
}

/* Custom Logo and Branding */
.navbar-brand img {
    max-height: 40px;
}

.navbar-brand::after {
    content: " - SoloYLibre Ultimate";
    font-weight: 600;
    color: var(--soloylibre-primary);
}

/* Custom Header */
.navbar {
    background: linear-gradient(135deg, var(--soloylibre-primary) 0%, var(--soloylibre-secondary) 100%) !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Sidebar Customization */
.sidebar {
    background: linear-gradient(180deg, var(--soloylibre-dark) 0%, #34495e 100%) !important;
}

.sidebar .nav-link {
    color: var(--soloylibre-light) !important;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background: rgba(255,255,255,0.1) !important;
    color: white !important;
}

.sidebar .nav-link.active {
    background: var(--soloylibre-primary) !important;
    color: white !important;
}

/* Card Customizations */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--soloylibre-primary), var(--soloylibre-secondary));
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

/* Button Customizations */
.btn-primary {
    background: linear-gradient(45deg, var(--soloylibre-primary), var(--soloylibre-secondary));
    border: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, var(--soloylibre-success), #58d68d);
    border: none;
    border-radius: 8px;
}

.btn-warning {
    background: linear-gradient(45deg, var(--soloylibre-warning), #f7dc6f);
    border: none;
    border-radius: 8px;
}

.btn-danger {
    background: linear-gradient(45deg, var(--soloylibre-accent), #ec7063);
    border: none;
    border-radius: 8px;
}

/* Table Customizations */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: var(--soloylibre-dark);
    color: white;
    border: none;
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.1);
}

/* Container Status Badges */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.badge-success {
    background: var(--soloylibre-success) !important;
}

.badge-warning {
    background: var(--soloylibre-warning) !important;
}

.badge-danger {
    background: var(--soloylibre-accent) !important;
}

.badge-info {
    background: var(--soloylibre-info) !important;
}

/* Custom Footer */
.footer {
    background: var(--soloylibre-dark);
    color: var(--soloylibre-light);
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer::before {
    content: "SoloYLibre Ultimate - Developed with ❤️ by Jose L Encarnacion (JoseTusabe)";
    display: block;
    text-align: center;
    font-weight: 500;
    margin-bottom: 1rem;
}

.footer::after {
    content: "📧 <EMAIL> | 📱 718-713-5500 | 📍 San Jose de Ocoa, Dom. Rep.";
    display: block;
    text-align: center;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Loading Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--soloylibre-light);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--soloylibre-primary), var(--soloylibre-secondary));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--soloylibre-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand::after {
        content: " - SYL";
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .footer::before,
    .footer::after {
        font-size: 0.8rem;
    }
}

/* Custom Dashboard Widgets */
.dashboard-widget {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-left: 4px solid var(--soloylibre-primary);
}

.dashboard-widget h5 {
    color: var(--soloylibre-dark);
    margin-bottom: 1rem;
}

.dashboard-widget .widget-icon {
    font-size: 2rem;
    color: var(--soloylibre-primary);
    margin-bottom: 1rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-running {
    background: var(--soloylibre-success);
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
}

.status-stopped {
    background: var(--soloylibre-accent);
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

.status-paused {
    background: var(--soloylibre-warning);
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
}

/* Photography Theme Elements (for Jose's passion) */
.photo-frame {
    border: 3px solid white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border-radius: 5px;
    transition: transform 0.3s ease;
}

.photo-frame:hover {
    transform: scale(1.05);
}

/* JEYKO AI Branding */
.jeyko-ai-badge {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    margin-left: 0.5rem;
}

.jeyko-ai-badge::before {
    content: "🤖 ";
}

/* Custom Animations for Container States */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.container-starting {
    animation: pulse 2s infinite;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dashboard-widget {
        background: #2c3e50;
        color: var(--soloylibre-light);
    }
    
    .dashboard-widget h5 {
        color: var(--soloylibre-light);
    }
}
