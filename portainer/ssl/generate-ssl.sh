#!/bin/bash

# SSL Certificate Generation for SoloYLibre Portainer
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>

set -e

echo "🔐 Generating SSL certificates for SoloYLibre Portainer..."

# Create SSL directory if it doesn't exist
mkdir -p ssl

# Generate private key
openssl genrsa -out ssl/portainer.key 4096

# Generate certificate signing request
openssl req -new -key ssl/portainer.key -out ssl/portainer.csr -subj "/C=DO/ST=San Jose de Ocoa/L=San Jose de Ocoa/O=SoloYLibre Web Dev/OU=JEYKO Ai/CN=portainer.soloylibre.com/emailAddress=<EMAIL>"

# Generate self-signed certificate (valid for 365 days)
openssl x509 -req -in ssl/portainer.csr -signkey ssl/portainer.key -out ssl/portainer.crt -days 365 -extensions v3_req -extfile <(cat <<EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = portainer.soloylibre.com
DNS.2 = localhost
DNS.3 = *.soloylibre.com
DNS.4 = josetusabe.com
DNS.5 = *.josetusabe.com
DNS.6 = 1and1photo.com
DNS.7 = *.1and1photo.com
DNS.8 = joselencarnacion.com
DNS.9 = *.joselencarnacion.com
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
)

# Set proper permissions
chmod 600 ssl/portainer.key
chmod 644 ssl/portainer.crt

# Clean up CSR file
rm ssl/portainer.csr

echo "✅ SSL certificates generated successfully!"
echo "📁 Certificate: ssl/portainer.crt"
echo "🔑 Private Key: ssl/portainer.key"
echo ""
echo "🌐 Domains covered:"
echo "   - portainer.soloylibre.com"
echo "   - localhost"
echo "   - *.soloylibre.com"
echo "   - josetusabe.com"
echo "   - *.josetusabe.com"
echo "   - 1and1photo.com"
echo "   - *.1and1photo.com"
echo "   - joselencarnacion.com"
echo "   - *.joselencarnacion.com"
echo ""
echo "📞 Contact: Jose L Encarnacion (JoseTusabe)"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
