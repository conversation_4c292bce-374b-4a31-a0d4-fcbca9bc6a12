#!/bin/bash

# SoloYLibre Portainer Customization Script
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>

set -e

echo "🎨 Customizing SoloYLibre Portainer Interface..."
echo "👨‍💻 Owner: <PERSON> (JoseTusabe)"
echo "📧 Email: <EMAIL>"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[CUSTOMIZE]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if <PERSON><PERSON><PERSON> is running
print_header "Checking Portainer status..."
if ! docker ps | grep -q "soloylibre-portainer"; then
    print_warning "Portainer is not running. Starting it first..."
    docker-compose up -d
    sleep 10
fi

# Create custom directory in Portainer volume
print_header "Setting up custom files..."
CONTAINER_NAME="soloylibre-portainer"

# Copy custom files to Portainer container
print_status "Copying custom home page..."
docker cp custom/home.html $CONTAINER_NAME:/public/
docker cp custom/soloylibre-theme.css $CONTAINER_NAME:/public/

# Create custom logo
print_header "Setting up custom branding..."
cat > custom/logo.svg << 'EOF'
<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="60" cy="60" r="55" fill="url(#grad1)" stroke="#fff" stroke-width="3"/>
  <text x="60" y="75" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">SYL</text>
  <text x="60" y="35" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">ULTIMATE</text>
</svg>
EOF

docker cp custom/logo.svg $CONTAINER_NAME:/public/

# Create custom JavaScript for enhanced functionality
print_header "Adding custom JavaScript..."
cat > custom/soloylibre-custom.js << 'EOF'
// SoloYLibre Ultimate Custom JavaScript
// Owner: Jose L Encarnacion (JoseTusabe)

(function() {
    'use strict';
    
    // Add custom branding
    function addCustomBranding() {
        const navbar = document.querySelector('.navbar-brand');
        if (navbar && !navbar.querySelector('.soloylibre-badge')) {
            const badge = document.createElement('span');
            badge.className = 'jeyko-ai-badge soloylibre-badge';
            badge.textContent = 'JEYKO AI';
            navbar.appendChild(badge);
        }
    }
    
    // Add owner information footer
    function addOwnerFooter() {
        if (!document.querySelector('.soloylibre-footer')) {
            const footer = document.createElement('div');
            footer.className = 'soloylibre-footer';
            footer.innerHTML = `
                <div style="background: #2c3e50; color: #ecf0f1; padding: 1rem; text-align: center; margin-top: 2rem;">
                    <p style="margin: 0; font-weight: 600;">SoloYLibre Ultimate - Container Management</p>
                    <p style="margin: 0; font-size: 0.9rem; opacity: 0.8;">
                        Developed with ❤️ by Jose L Encarnacion (JoseTusabe)<br>
                        📧 <EMAIL> | 📱 ************ | 📍 San Jose de Ocoa, Dom. Rep.
                    </p>
                    <p style="margin: 0; font-size: 0.8rem; opacity: 0.6;">
                        🌐 <a href="https://soloylibre.com" target="_blank" style="color: #3498db;">SoloYLibre</a> | 
                        <a href="https://josetusabe.com" target="_blank" style="color: #3498db;">JoseTusabe</a> | 
                        <a href="https://1and1photo.com" target="_blank" style="color: #3498db;">1and1Photo</a> | 
                        <a href="https://joselencarnacion.com" target="_blank" style="color: #3498db;">Jose L Encarnacion</a>
                    </p>
                </div>
            `;
            document.body.appendChild(footer);
        }
    }
    
    // Add custom dashboard stats
    function addDashboardStats() {
        const mainContent = document.querySelector('.main-content');
        if (mainContent && !document.querySelector('.soloylibre-stats')) {
            const statsDiv = document.createElement('div');
            statsDiv.className = 'soloylibre-stats';
            statsDiv.innerHTML = `
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                    <h5>🚀 SoloYLibre Ultimate Dashboard</h5>
                    <p style="margin: 0; opacity: 0.9;">JEYKO AI Development Environment | Server: Synology RS3618xs (56GB RAM, 36TB)</p>
                </div>
            `;
            mainContent.insertBefore(statsDiv, mainContent.firstChild);
        }
    }
    
    // Initialize customizations
    function initCustomizations() {
        addCustomBranding();
        addOwnerFooter();
        addDashboardStats();
    }
    
    // Run customizations when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCustomizations);
    } else {
        initCustomizations();
    }
    
    // Re-run customizations on navigation (for SPA)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                setTimeout(initCustomizations, 500);
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
})();
EOF

docker cp custom/soloylibre-custom.js $CONTAINER_NAME:/public/

# Create nginx configuration for custom home page
print_header "Configuring custom routing..."
cat > custom/nginx-custom.conf << 'EOF'
# Custom nginx configuration for SoloYLibre home page
location /home {
    alias /public/home.html;
    try_files $uri $uri/ =404;
}

location /custom {
    alias /public/;
    try_files $uri $uri/ =404;
}
EOF

# Restart Portainer to apply changes
print_header "Applying customizations..."
docker-compose restart

# Wait for Portainer to start
print_status "Waiting for Portainer to restart..."
sleep 15

# Check if Portainer is running
if docker ps | grep -q "soloylibre-portainer"; then
    print_status "✅ Customizations applied successfully!"
    echo ""
    echo "🎨 Custom Features Added:"
    echo "   ✅ Custom SoloYLibre home page"
    echo "   ✅ Custom CSS theme"
    echo "   ✅ Custom branding and logo"
    echo "   ✅ Owner information footer"
    echo "   ✅ JEYKO AI branding"
    echo "   ✅ Enhanced dashboard stats"
    echo ""
    echo "🌐 Access URLs:"
    echo "   Main Dashboard: http://localhost:9000"
    echo "   Custom Home: http://localhost:9000/home"
    echo "   Custom Assets: http://localhost:9000/custom/"
    echo ""
    echo "🔐 Updated Login Credentials:"
    echo "   Username: soloylibre_ultimate"
    echo "   Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?"
    echo ""
    echo "👨‍💻 Customized for: Jose L Encarnacion (JoseTusabe)"
    echo "📧 Contact: <EMAIL>"
    echo "📱 Phone: ************"
else
    print_warning "❌ Failed to restart Portainer. Check logs:"
    docker-compose logs
fi
