#!/bin/bash

# SoloYLibre Portainer Setup Script
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>
# Phone: ************
# Location: San Jose de <PERSON>, Dom. Rep.

set -e

echo "🚀 Setting up SoloYLibre Portainer Environment..."
echo "👨‍💻 Owner: <PERSON> (JoseTusabe)"
echo "📧 Email: <EMAIL>"
echo "📱 Phone: ************"
echo "📍 Location: San Jose de Ocoa, Dom. Rep."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Check if <PERSON><PERSON> is running
print_header "Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi
print_status "Docker is running ✅"

# Create necessary directories
print_header "Creating directory structure..."
mkdir -p data
mkdir -p backups
mkdir -p logs
mkdir -p ssl
mkdir -p config
print_status "Directories created ✅"

# Generate SSL certificates
print_header "Generating SSL certificates..."
if [ ! -f "ssl/portainer.crt" ] || [ ! -f "ssl/portainer.key" ]; then
    chmod +x ssl/generate-ssl.sh
    ./ssl/generate-ssl.sh
else
    print_status "SSL certificates already exist ✅"
fi

# Create Docker network if it doesn't exist
print_header "Setting up Docker network..."
if ! docker network ls | grep -q "soloylibre-network"; then
    docker network create soloylibre-network
    print_status "Created soloylibre-network ✅"
else
    print_status "Network soloylibre-network already exists ✅"
fi

# Generate admin password hash
print_header "Setting up admin credentials..."
ADMIN_PASSWORD="SoloYLibre2024!JeykoAi"
ADMIN_PASSWORD_HASH=$(docker run --rm httpd:2.4-alpine htpasswd -nbB admin "$ADMIN_PASSWORD" | cut -d ":" -f 2)
echo "$ADMIN_PASSWORD_HASH" > config/admin_password
print_status "Admin password configured ✅"

# Set proper permissions
print_header "Setting file permissions..."
chmod 600 config/admin_password
chmod 600 config/credentials.env
chmod 600 ssl/portainer.key
chmod 644 ssl/portainer.crt
print_status "Permissions set ✅"

# Stop existing Portainer if running
print_header "Checking for existing Portainer containers..."
if docker ps -a | grep -q "portainer"; then
    print_warning "Stopping existing Portainer containers..."
    docker stop $(docker ps -a -q --filter="name=portainer") 2>/dev/null || true
    docker rm $(docker ps -a -q --filter="name=portainer") 2>/dev/null || true
fi

# Start Portainer
print_header "Starting SoloYLibre Portainer..."
docker-compose up -d

# Wait for Portainer to start
print_status "Waiting for Portainer to start..."
sleep 10

# Check if Portainer is running
if docker ps | grep -q "soloylibre-portainer"; then
    print_status "Portainer is running successfully! ✅"
    echo ""
    echo "🌐 Access URLs:"
    echo "   HTTP:  http://localhost:9000"
    echo "   HTTPS: https://localhost:9443"
    echo "   Domain: https://portainer.soloylibre.com (if DNS configured)"
    echo ""
    echo "🔐 Login Credentials:"
    echo "   Username: admin"
    echo "   Password: SoloYLibre2024!JeykoAi"
    echo ""
    echo "👤 Alternative User:"
    echo "   Username: josetusabe"
    echo "   Password: JoseTusabe2024!Photography"
    echo ""
    echo "📊 Container Status:"
    docker ps --filter="name=soloylibre-portainer"
    echo ""
    echo "🎉 SoloYLibre Portainer setup completed successfully!"
    echo "📧 Support: <EMAIL>"
    echo "📱 Phone: ************"
else
    print_error "Failed to start Portainer. Check logs:"
    docker-compose logs
    exit 1
fi
