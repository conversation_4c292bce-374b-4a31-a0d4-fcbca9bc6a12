#!/bin/bash

# SoloYLibre Portainer Backup Script
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>

set -e

# Configuration
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="portainer_backup_${TIMESTAMP}"
CONTAINER_NAME="soloylibre-portainer"

echo "🔄 Starting SoloYLibre Portainer backup..."
echo "📅 Timestamp: $TIMESTAMP"
echo "👨‍💻 Owner: Jose <PERSON> Encarnacion (JoseTusabe)"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Create backup archive
echo "📦 Creating backup archive..."
tar -czf "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" \
    --exclude='./backups' \
    --exclude='./logs/*.log' \
    ./data \
    ./config \
    ./ssl \
    docker-compose.yml

# Export Portainer data if container is running
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "💾 Exporting Portainer data..."
    docker exec "$CONTAINER_NAME" tar -czf /tmp/portainer_data.tar.gz /data
    docker cp "$CONTAINER_NAME:/tmp/portainer_data.tar.gz" "${BACKUP_DIR}/${BACKUP_NAME}_data.tar.gz"
    docker exec "$CONTAINER_NAME" rm /tmp/portainer_data.tar.gz
fi

# Create backup info file
cat > "${BACKUP_DIR}/${BACKUP_NAME}_info.txt" << EOF
SoloYLibre Portainer Backup Information
======================================
Backup Date: $(date)
Backup Name: $BACKUP_NAME
Owner: Jose L Encarnacion (JoseTusabe)
Email: <EMAIL>
Phone: ************
Location: San Jose de Ocoa, Dom. Rep.

Websites:
- https://soloylibre.com
- https://josetusabe.com
- https://1and1photo.com
- https://joselencarnacion.com

Backup Contents:
- Portainer configuration
- SSL certificates
- Data volumes
- Docker compose configuration

Restore Instructions:
1. Extract backup: tar -xzf ${BACKUP_NAME}.tar.gz
2. Run setup script: ./scripts/setup-portainer.sh
3. Restore data if needed: docker cp ${BACKUP_NAME}_data.tar.gz container:/data/
EOF

echo "✅ Backup completed successfully!"
echo "📁 Backup location: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
echo "📋 Info file: ${BACKUP_DIR}/${BACKUP_NAME}_info.txt"

# Clean up old backups (keep last 7 days)
echo "🧹 Cleaning up old backups..."
find "$BACKUP_DIR" -name "portainer_backup_*.tar.gz" -mtime +7 -delete
find "$BACKUP_DIR" -name "portainer_backup_*_data.tar.gz" -mtime +7 -delete
find "$BACKUP_DIR" -name "portainer_backup_*_info.txt" -mtime +7 -delete

echo "🎉 Backup process completed!"
