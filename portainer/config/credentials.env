# SoloYLibre Portainer Credentials
# Owner: <PERSON> (JoseTusabe)
# Email: <EMAIL>
# Phone: ************
# Location: San Jose de Ocoa, Dom. Rep.

# Portainer Admin Credentials
PORTAINER_ADMIN_USERNAME=admin
PORTAINER_ADMIN_EMAIL=<EMAIL>
PORTAINER_ADMIN_PASSWORD=SoloYLibre2024!JeykoAi

# Alternative Admin User
PORTAINER_USER_USERNAME=josetusabe
PORTAINER_USER_EMAIL=<EMAIL>
PORTAINER_USER_PASSWORD=JoseTusabe2024!Photography

# Database Credentials (if using external DB)
PORTAINER_DB_HOST=localhost
PORTAINER_DB_PORT=5432
PORTAINER_DB_NAME=portainer
PORTAINER_DB_USER=portainer_user
PORTAINER_DB_PASSWORD=SoloYLibre_Portainer_DB_2024

# SSL Configuration
PORTAINER_SSL_CERT_PATH=/ssl/portainer.crt
PORTAINER_SSL_KEY_PATH=/ssl/portainer.key

# Network Configuration
PORTAINER_NETWORK=soloylibre-network
PORTAINER_HTTP_PORT=9000
PORTAINER_HTTPS_PORT=9443
PORTAINER_AGENT_PORT=9001

# Domain Configuration
PORTAINER_DOMAIN=portainer.soloylibre.com
PORTAINER_PUBLIC_URL=https://portainer.soloylibre.com

# Backup Configuration
PORTAINER_BACKUP_PATH=./backups
PORTAINER_BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# Logging
PORTAINER_LOG_LEVEL=INFO
PORTAINER_LOG_FILE=/data/portainer.log

# Security
PORTAINER_SESSION_TIMEOUT=8h
PORTAINER_ENABLE_TELEMETRY=false
PORTAINER_HIDE_LABELS=true

# Integration URLs
SOLOYLIBRE_MAIN_SITE=https://soloylibre.com
JOSETUSABE_SITE=https://josetusabe.com
PHOTOGRAPHY_SITE=https://1and1photo.com
PERSONAL_SITE=https://joselencarnacion.com
