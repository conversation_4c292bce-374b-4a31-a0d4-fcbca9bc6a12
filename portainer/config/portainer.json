{"version": "2.0", "logo": "https://soloylibre.com/logo.png", "authentication": {"method": "internal"}, "features": {"enableHostManagementFeatures": true, "enableVolumeBrowserForNonAdminUsers": false}, "settings": {"logoURL": "https://soloylibre.com/logo.png", "blackListedLabels": [], "displayDonationHeader": false, "displayExternalContributors": false, "enableTelemetry": false, "enableEdgeComputeFeatures": true, "userSessionTimeout": "8h", "kubernetesShellImage": "portainer/kubectl-shell", "templatesURL": "https://raw.githubusercontent.com/portainer/templates/master/templates-2.0.json"}, "endpoints": [{"name": "SoloYLibre Local Docker", "type": 1, "url": "unix:///var/run/docker.sock", "groupId": 1, "publicURL": "https://portainer.soloylibre.com", "tags": ["<PERSON><PERSON><PERSON><PERSON>", "local", "development"]}], "groups": [{"id": 1, "name": "SoloYLibre Development", "description": "Jose <PERSON> Encarnacion - SoloYLibre Web Development Environment", "tags": ["<PERSON><PERSON><PERSON><PERSON>", "jose<PERSON><PERSON>", "development"]}], "teams": [{"name": "SoloYLibre Developers", "description": "Main development team for SoloYLibre projects"}], "users": [{"username": "soloylibre_ultimate", "email": "<EMAIL>", "role": 1}, {"username": "jose<PERSON><PERSON>", "email": "<EMAIL>", "role": 1}]}