# SoloYLibre Portainer Setup

Complete Portainer deployment for SoloYLibre Web Development environment.

## 👨‍💻 Owner Information
- **Name**: <PERSON> (JoseTusabe)
- **Email**: <EMAIL>
- **Phone**: ************
- **Location**: San Jose <PERSON>, Dom. Rep.
- **Passion**: Photography and Technology

## 🌐 Websites
- [SoloYLibre](https://soloylibre.com)
- [<PERSON><PERSON><PERSON><PERSON>](https://josetusabe.com)
- [1and1Photo](https://1and1photo.com)
- [<PERSON> Encarnacion](https://joselencarnacion.com)

## 🚀 Quick Start

### 1. Automated Setup
```bash
cd portainer
chmod +x scripts/setup-portainer.sh
./scripts/setup-portainer.sh
```

### 2. Manual Setup
```bash
# Generate SSL certificates
chmod +x ssl/generate-ssl.sh
./ssl/generate-ssl.sh

# Create Docker network
docker network create soloylibre-network

# Start Portainer
docker-compose up -d
```

## 🔐 Default Credentials

### Admin User
- **Username**: `admin`
- **Email**: `<EMAIL>`
- **Password**: `SoloYLibre2024!JeykoAi`

### Alternative User
- **Username**: `josetusabe`
- **Email**: `<EMAIL>`
- **Password**: `JoseTusabe2024!Photography`

## 🌐 Access URLs

- **HTTP**: http://localhost:9000
- **HTTPS**: https://localhost:9443
- **Domain**: https://portainer.soloylibre.com (requires DNS setup)
- **Agent**: http://localhost:9001

## 📁 Directory Structure

```
portainer/
├── docker-compose.yml          # Main compose file
├── config/
│   ├── admin_password         # Hashed admin password
│   ├── portainer.json         # Portainer configuration
│   └── credentials.env        # Environment variables
├── ssl/
│   ├── generate-ssl.sh        # SSL certificate generator
│   ├── portainer.crt          # SSL certificate
│   └── portainer.key          # SSL private key
├── scripts/
│   ├── setup-portainer.sh     # Automated setup script
│   └── backup-portainer.sh    # Backup script
├── data/                      # Portainer data volume
├── backups/                   # Backup storage
└── logs/                      # Log files
```

## 🔧 Configuration Features

- ✅ SSL/TLS encryption
- ✅ Custom branding with SoloYLibre logo
- ✅ Traefik integration
- ✅ Agent support for multi-host management
- ✅ Automated backups
- ✅ Security hardening
- ✅ Custom templates
- ✅ Network isolation

## 🛠️ Management Commands

### Start Services
```bash
docker-compose up -d
```

### Stop Services
```bash
docker-compose down
```

### View Logs
```bash
docker-compose logs -f
```

### Backup Data
```bash
./scripts/backup-portainer.sh
```

### Update Portainer
```bash
docker-compose pull
docker-compose up -d
```

## 🔒 Security Features

- Password-protected admin access
- SSL/TLS encryption
- Network isolation
- Volume mount restrictions
- Security labels and policies
- Session timeout configuration

## 📊 Monitoring

Portainer includes built-in monitoring for:
- Container status and health
- Resource usage (CPU, Memory, Network)
- Volume usage
- Network traffic
- Event logs

## 🔄 Backup Strategy

Automated daily backups include:
- Portainer configuration
- SSL certificates
- Data volumes
- User settings
- Templates and stacks

## 🌐 Domain Configuration

To use custom domains, add these entries to your DNS:
```
portainer.soloylibre.com    A    YOUR_SERVER_IP
```

## 📞 Support

For support or questions:
- **Email**: <EMAIL>
- **Phone**: ************
- **Location**: San Jose de Ocoa, Dom. Rep.

## 🎯 JEYKO AI Integration

This setup is optimized for JEYKO AI development workflows and includes:
- AI-friendly container management
- Development environment templates
- Automated deployment pipelines
- Photography workflow containers

---

**Developed with ❤️ by Jose L Encarnacion (JoseTusabe)**  
*Passionate about Photography and Technology*
