'use client'

// SoloYLibre Ultimate - Main Page Component
// Owner: <PERSON> (JoseTusabe)
// Email: <EMAIL>
// Phone: ************
// Location: San Jose de Ocoa, Dom. Rep.

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Heart, 
  ShoppingCart, 
  GraduationCap, 
  Shield, 
  Camera, 
  Code, 
  Globe, 
  Phone, 
  Mail, 
  MapPin,
  Server,
  Cpu,
  HardDrive
} from 'lucide-react'

interface PlatformStats {
  total_users: number
  active_dating_profiles: number
  active_products: number
  published_courses: number
}

export default function HomePage() {
  const [stats, setStats] = useState<PlatformStats | null>(null)
  const [selectedTheme, setSelectedTheme] = useState<string>('')

  useEffect(() => {
    // Fetch platform stats
    fetch('/api/v1/admin/stats')
      .then(res => res.json())
      .then(data => setStats(data.stats))
      .catch(err => console.error('Failed to fetch stats:', err))
  }, [])

  const themes = [
    {
      id: 'dating',
      title: '💕 Dating Platform',
      description: 'Modern Tinder-like interface with AI-powered matching',
      color: 'from-pink-500 to-rose-500',
      icon: Heart,
      features: ['Swipe Matching', 'Real-time Chat', 'Location Discovery', 'Premium Features']
    },
    {
      id: 'ecommerce',
      title: '🛒 E-commerce Platform',
      description: 'Amazon-like shopping experience with course marketplace',
      color: 'from-blue-500 to-indigo-500',
      icon: ShoppingCart,
      features: ['Product Catalog', 'Secure Payments', 'Order Management', 'Digital Delivery']
    },
    {
      id: 'courses',
      title: '🎓 Learning Management',
      description: 'Interactive courses with progress tracking and certificates',
      color: 'from-green-500 to-emerald-500',
      icon: GraduationCap,
      features: ['Video Courses', 'Progress Tracking', 'Certificates', 'Analytics']
    },
    {
      id: 'admin',
      title: '🛡️ Admin Dashboard',
      description: 'Complete platform control with analytics and moderation',
      color: 'from-purple-500 to-violet-500',
      icon: Shield,
      features: ['User Management', 'Content Moderation', 'Analytics', 'System Config']
    }
  ]

  const ownerInfo = {
    name: 'Jose L Encarnacion (JoseTusabe)',
    email: '<EMAIL>',
    phone: '************',
    location: 'San Jose de Ocoa, Dom. Rep.',
    passion: 'Photography and Technology',
    server: 'Synology RS3618xs',
    specs: '56GB RAM | 36TB Storage',
    websites: [
      { name: 'SoloYLibre', url: 'https://soloylibre.com' },
      { name: 'JoseTusabe', url: 'https://josetusabe.com' },
      { name: '1and1Photo', url: 'https://1and1photo.com' },
      { name: 'Jose L Encarnacion', url: 'https://joselencarnacion.com' }
    ]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
              🌟 SoloYLibre
              <span className="bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">
                Ultimate
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto">
              Multi-Application Platform combining Dating, E-commerce, Courses, and Administration
            </p>
            
            {/* Owner Info */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 max-w-4xl mx-auto mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-white">
                    <Camera className="w-5 h-5 text-pink-400" />
                    <span className="font-semibold">{ownerInfo.name}</span>
                  </div>
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-gray-300">
                    <Mail className="w-4 h-4" />
                    <span>{ownerInfo.email}</span>
                  </div>
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-gray-300">
                    <Phone className="w-4 h-4" />
                    <span>{ownerInfo.phone}</span>
                  </div>
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-gray-300">
                    <MapPin className="w-4 h-4" />
                    <span>{ownerInfo.location}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-white">
                    <Server className="w-5 h-5 text-blue-400" />
                    <span className="font-semibold">{ownerInfo.server}</span>
                  </div>
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-gray-300">
                    <Cpu className="w-4 h-4" />
                    <span>{ownerInfo.specs}</span>
                  </div>
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-gray-300">
                    <Code className="w-4 h-4" />
                    <span>{ownerInfo.passion}</span>
                  </div>
                  <div className="flex items-center justify-center md:justify-start space-x-2 text-gray-300">
                    <HardDrive className="w-4 h-4" />
                    <span>JEYKO AI Development</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Platform Stats */}
            {stats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                  <div className="text-2xl font-bold text-white">{stats.total_users}</div>
                  <div className="text-sm text-gray-300">Total Users</div>
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                  <div className="text-2xl font-bold text-pink-400">{stats.active_dating_profiles}</div>
                  <div className="text-sm text-gray-300">Dating Profiles</div>
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                  <div className="text-2xl font-bold text-blue-400">{stats.active_products}</div>
                  <div className="text-sm text-gray-300">Products</div>
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                  <div className="text-2xl font-bold text-green-400">{stats.published_courses}</div>
                  <div className="text-sm text-gray-300">Courses</div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </header>

      {/* Theme Selection */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl font-bold text-white mb-4">Choose Your Experience</h2>
          <p className="text-xl text-gray-300">Select a platform to explore</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {themes.map((theme, index) => {
            const IconComponent = theme.icon
            return (
              <motion.div
                key={theme.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 * index }}
                className={`relative group cursor-pointer ${
                  selectedTheme === theme.id ? 'scale-105' : ''
                }`}
                onClick={() => setSelectedTheme(theme.id)}
              >
                <div className={`absolute inset-0 bg-gradient-to-r ${theme.color} rounded-2xl blur-xl opacity-25 group-hover:opacity-40 transition-opacity`} />
                <div className="relative bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all">
                  <div className="text-center mb-4">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${theme.color} mb-4`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">{theme.title}</h3>
                    <p className="text-gray-300 text-sm">{theme.description}</p>
                  </div>
                  
                  <div className="space-y-2">
                    {theme.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2 text-sm text-gray-300">
                        <div className="w-1.5 h-1.5 bg-white rounded-full" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <button className={`w-full mt-6 py-3 px-4 rounded-xl bg-gradient-to-r ${theme.color} text-white font-semibold hover:shadow-lg transition-all`}>
                    Enter Platform
                  </button>
                </div>
              </motion.div>
            )
          })}
        </div>
      </section>

      {/* Websites */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-8">Visit Our Websites</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {ownerInfo.websites.map((website, index) => (
              <a
                key={index}
                href={website.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 bg-white/10 backdrop-blur-md rounded-xl px-6 py-3 text-white hover:bg-white/20 transition-all"
              >
                <Globe className="w-4 h-4" />
                <span>{website.name}</span>
              </a>
            ))}
          </div>
        </motion.div>
      </section>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-md border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-300">
            <p className="mb-2">
              Built with ❤️ by <span className="text-white font-semibold">{ownerInfo.name}</span>
            </p>
            <p className="text-sm">
              Passionate about Photography and Technology | Powered by Synology RS3618xs
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
