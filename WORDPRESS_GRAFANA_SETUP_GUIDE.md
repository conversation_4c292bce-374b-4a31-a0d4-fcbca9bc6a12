# 🔗 WordPress + Grafana Monitoring Setup Guide

## 📋 **Complete Integration for SoloYLibre Ultimate**

### 👨‍💻 **Owner Information**
- **Name**: <PERSON> (JoseTusabe)
- **Email**: <EMAIL>
- **Phone**: ************
- **Location**: San Jose <PERSON>, Dom. Rep.
- **Server**: Synology RS3618xs | 56GB RAM | 36TB

## 🎯 **Step-by-Step Setup**

### **Step 1: Deploy Both Stacks in Portainer**

1. **Deploy WordPress Stack**:
   - Use the `WORDPRESS_PORTAINER_PASTE.yml` configuration
   - Stack name: `soloylibre_ultimate_wordpress`
   - Wait for all containers to be healthy

2. **Deploy Grafana Stack**:
   - Use the `PORTAINER_PASTE_READY.yml` configuration  
   - Stack name: `soloylibre_ultimate_grafana`
   - Wait for Grafana to be accessible

### **Step 2: Configure Grafana Data Sources**

1. **Access Grafana**: http://localhost:3000
2. **Login**: 
   - Username: `soloylibre_ultimate`
   - Password: `PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?`

3. **Add Data Sources**:
   - Go to **Configuration** → **Data Sources**
   - Click **"Add data source"**

#### **Prometheus Data Source**
```
Name: Prometheus - WordPress Monitoring
Type: Prometheus
URL: http://prometheus:9090
Access: Server (default)
Scrape interval: 15s
Query timeout: 60s
HTTP Method: POST
```

#### **MySQL Data Source**
```
Name: MySQL - WordPress Database
Type: MySQL
Host: soloylibre_ultimate_wordpress_db_josetusabe:3306
Database: soloylibre_ultimate_db
User: soloylibre_ultimate
Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
```

#### **Redis Data Source**
```
Name: Redis - WordPress Cache
Type: Redis
Address: soloylibre_ultimate_redis_josetusabe:6379
Password: SoloYLibre2024Redis
```

### **Step 3: Import WordPress Dashboard**

1. **Go to**: **"+"** → **Import**
2. **Copy and paste** the contents of `wordpress-grafana-monitoring.json`
3. **Click**: **"Load"**
4. **Select**: Your configured data sources
5. **Click**: **"Import"**

### **Step 4: Configure WordPress Monitoring Plugin**

1. **Access WordPress Admin**: http://localhost:1051/wp-admin
2. **Install Monitoring Plugin**:
   ```
   Plugin: WP Statistics or MonsterInsights
   Purpose: Generate metrics for Grafana
   ```

3. **Install Prometheus Exporter Plugin**:
   ```
   Plugin: Prometheus Exporter for WordPress
   Configuration: Enable metrics endpoint
   Endpoint: /wp-json/prometheus/metrics
   ```

### **Step 5: Update Prometheus Configuration**

Add this to your Prometheus config:
```yaml
scrape_configs:
  - job_name: 'wordpress-metrics'
    static_configs:
      - targets: ['soloylibre_ultimate_wordpress_josetusabe:80']
    metrics_path: '/wp-json/prometheus/metrics'
    scrape_interval: 30s

  - job_name: 'wordpress-containers'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
```

## 📊 **Available Metrics**

### **Container Metrics**
- WordPress container CPU/Memory usage
- Database container performance
- Redis cache performance
- Network traffic and I/O

### **WordPress Specific Metrics**
- Page load times
- Active users
- Post/Page counts
- Plugin status
- Theme information
- Database queries

### **Database Metrics**
- Connection count
- Query performance
- Table sizes
- Index usage

### **Cache Metrics**
- Redis hit/miss ratio
- Cache size
- Memory usage
- Key statistics

## 🚨 **Alerts Configuration**

### **Critical Alerts**
- WordPress container down
- Database container down
- Site not responding
- High error rates

### **Warning Alerts**
- High CPU usage (>80%)
- High memory usage (>85%)
- Slow response times (>2s)
- Cache miss rate high (>20%)

## 🔧 **Custom Queries Examples**

### **WordPress Performance**
```promql
# Average response time
avg(wordpress_request_duration_seconds)

# Requests per second
rate(wordpress_requests_total[5m])

# Error rate
rate(wordpress_requests_total{status=~"5.."}[5m]) / rate(wordpress_requests_total[5m])
```

### **Database Performance**
```promql
# Database connections
mysql_global_status_threads_connected

# Query rate
rate(mysql_global_status_questions[5m])

# Slow queries
rate(mysql_global_status_slow_queries[5m])
```

### **Container Resources**
```promql
# WordPress CPU usage
rate(container_cpu_usage_seconds_total{name=~".*wordpress.*"}[5m]) * 100

# WordPress memory usage
container_memory_usage_bytes{name=~".*wordpress.*"} / 1024 / 1024
```

## 📁 **Files Provided**

1. **`wordpress-grafana-monitoring.json`** - Complete dashboard
2. **`grafana-datasources-config.json`** - Data source configuration
3. **`wordpress-monitoring-setup.json`** - Complete monitoring setup
4. **`WORDPRESS_GRAFANA_SETUP_GUIDE.md`** - This guide

## 🌐 **Access URLs**

- **WordPress**: http://localhost:1051
- **WordPress Admin**: http://localhost:1051/wp-admin
- **Grafana**: http://localhost:3000
- **phpMyAdmin**: http://localhost:2051
- **Prometheus**: http://localhost:9090

## 🔐 **Credentials**

### **WordPress & Grafana**
- **Username**: `soloylibre_ultimate`
- **Password**: `PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?`

### **Database**
- **User**: `soloylibre_ultimate`
- **Password**: `PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?`
- **Root Password**: `SoloYLibre2024RootDB!JoseTusabe`

## 🎉 **Result**

After setup, you'll have:
- ✅ Complete WordPress monitoring dashboard
- ✅ Real-time performance metrics
- ✅ Container resource monitoring
- ✅ Database performance tracking
- ✅ Cache monitoring
- ✅ Automated alerts
- ✅ Custom SoloYLibre branding

**Perfect monitoring solution for Jose L Encarnacion's WordPress environment!**

---

*Developed with ❤️ by Jose L Encarnacion (JoseTusabe)*  
*Passionate about Photography and Technology*
