version: '3.9'

services:
  # WordPress - Main application
  wordpress:
    image: wordpress:php8.2-apache
    container_name: soloylibre_wordpress_josetusabe
    hostname: soloylibre-wordpress
    ports:
      - "1052:80"
    depends_on:
      - database
      - cache
    environment:
      TZ: America/New_York
      WORDPRESS_DB_HOST: database
      WORDPRESS_DB_USER: soloylibre_ultimate
      WORDPRESS_DB_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      WORDPRESS_DB_NAME: soloylibre_db
      WORDPRESS_TABLE_PREFIX: wp_soloylibre_
      WORDPRESS_DEBUG: 0
    volumes:
      - wordpress_data:/var/www/html
    restart: unless-stopped

  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: soloylibre_database_josetusabe
    hostname: soloylibre-database
    ports:
      - "5433:5432"
    environment:
      TZ: America/New_York
      POSTGRES_DB: soloylibre_db
      POSTGRES_USER: soloylibre_ultimate
      POSTGRES_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      POSTGRES_INITDB_ARGS: "--encoding=UTF8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Redis Cache
  cache:
    image: redis:7-alpine
    container_name: soloylibre_cache_josetusabe
    hostname: soloylibre-cache
    ports:
      - "6380:6379"
    command: redis-server --requirepass "SoloYLibre2024Redis" --appendonly yes
    environment:
      TZ: America/New_York
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # phpMyAdmin - Database management
  phpmyadmin:
    image: phpmyadmin:latest
    container_name: soloylibre_phpmyadmin_josetusabe
    hostname: soloylibre-phpmyadmin
    ports:
      - "2051:80"
    depends_on:
      - database
    environment:
      TZ: America/New_York
      PMA_HOST: database
      PMA_PORT: 5432
      PMA_USER: soloylibre_ultimate
      PMA_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      UPLOAD_LIMIT: 1024M
    restart: unless-stopped

  # Grafana - Monitoring dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_grafana_josetusabe
    hostname: soloylibre-grafana
    ports:
      - "3001:3000"
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_DEFAULT_THEME: dark
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-worldmap-panel
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

  # Auto-Setup Container - Configures everything automatically
  auto-setup:
    image: alpine:latest
    container_name: soloylibre_auto_setup_josetusabe
    hostname: soloylibre-auto-setup
    depends_on:
      - wordpress
      - grafana
      - database
      - cache
    volumes:
      - wordpress_data:/var/www/html
    environment:
      TZ: America/New_York
    command: >
      sh -c "
      echo '🚀 SoloYLibre Ultimate - Automatic Setup Starting...' &&
      echo '👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)' &&
      echo '📧 Email: <EMAIL>' &&
      echo '📱 Phone: ************' &&
      echo '📍 Location: San Jose de Ocoa, Dom. Rep.' &&
      echo '🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB' &&
      echo '' &&
      
      # Install required tools
      apk add --no-cache curl wget &&
      
      # Wait for services to be ready
      echo '⏳ Waiting for services to be ready...' &&
      sleep 120 &&
      
      # Download WP-CLI
      echo '📥 Installing WordPress CLI...' &&
      cd /var/www/html &&
      curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/phar/wp-cli.phar &&
      chmod +x wp-cli.phar &&
      mv wp-cli.phar /usr/local/bin/wp &&
      
      # Wait for WordPress to be fully ready
      echo '⏳ Waiting for WordPress to be ready...' &&
      sleep 60 &&
      
      # Install WordPress core
      echo '🔧 Installing WordPress...' &&
      wp core install --url='http://localhost:1052' --title='SoloYLibre Ultimate - Jose L Encarnacion' --admin_user='soloylibre_ultimate' --admin_password='PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?' --admin_email='<EMAIL>' --allow-root --skip-email &&
      
      # Install essential plugins
      echo '🔌 Installing WordPress plugins...' &&
      wp plugin install contact-form-7 --activate --allow-root &&
      wp plugin install elementor --activate --allow-root &&
      wp plugin install woocommerce --activate --allow-root &&
      
      # Create sample content
      echo '📝 Creating sample content...' &&
      wp post create --post_type=page --post_title='About SoloYLibre Ultimate' --post_content='<h1>Welcome to SoloYLibre Ultimate</h1><p><strong>Owner:</strong> Jose L Encarnacion (JoseTusabe)</p><p><strong>Email:</strong> <EMAIL></p><p><strong>Phone:</strong> ************</p><p><strong>Location:</strong> San Jose de Ocoa, Dom. Rep.</p><p><strong>Server:</strong> Synology RS3618xs | 56GB RAM | 36TB</p><p><strong>Passion:</strong> Photography and Technology</p><h2>Features</h2><ul><li>Complete WordPress Platform</li><li>Grafana Monitoring</li><li>E-commerce Ready (WooCommerce)</li><li>Photography Portfolio</li><li>JEYKO AI Development Ready</li></ul><h2>Websites</h2><ul><li><a href=\"https://soloylibre.com\">SoloYLibre</a></li><li><a href=\"https://josetusabe.com\">JoseTusabe</a></li><li><a href=\"https://1and1photo.com\">1and1Photo</a></li><li><a href=\"https://joselencarnacion.com\">Jose L Encarnacion</a></li></ul>' --post_status=publish --allow-root &&
      
      echo '✅ WordPress setup completed!' &&
      echo '✅ Grafana ready!' &&
      echo '✅ All services connected and ready!' &&
      echo '' &&
      echo '🎉 SoloYLibre Ultimate Installation Complete!' &&
      echo '' &&
      echo '🌐 ACCESS INFORMATION:' &&
      echo '   WordPress: http://localhost:1052' &&
      echo '   WordPress Admin: http://localhost:1052/wp-admin' &&
      echo '   Grafana: http://localhost:3001' &&
      echo '   phpMyAdmin: http://localhost:2051' &&
      echo '' &&
      echo '🔐 LOGIN CREDENTIALS:' &&
      echo '   Username: soloylibre_ultimate' &&
      echo '   Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?' &&
      echo '   Email: <EMAIL>' &&
      echo '' &&
      echo '👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)' &&
      echo '📧 Email: <EMAIL>' &&
      echo '📱 Phone: ************' &&
      echo '📍 Location: San Jose de Ocoa, Dom. Rep.' &&
      echo '🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB' &&
      echo '' &&
      echo '🚀 Platform ready for your photography business and JEYKO AI development!' &&
      echo '🎯 Features: WordPress + WooCommerce + Grafana + Database Management' &&
      echo '' &&
      echo '✅ Installation completed successfully!' &&
      
      # Keep container running to show logs
      tail -f /dev/null
      "
    restart: "no"

volumes:
  wordpress_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_network
