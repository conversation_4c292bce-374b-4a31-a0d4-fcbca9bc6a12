{"apiVersion": 1, "datasources": [{"name": "Prometheus - WordPress Monitoring", "type": "prometheus", "access": "proxy", "url": "http://prometheus:9090", "isDefault": true, "editable": true, "jsonData": {"timeInterval": "5s", "queryTimeout": "60s", "httpMethod": "POST", "customQueryParameters": "owner=josetusabe&environment=wordpress"}, "secureJsonData": {}, "version": 1, "uid": "prometheus-wordpress"}, {"name": "Redis - <PERSON><PERSON><PERSON>", "type": "redis-datasource", "access": "proxy", "url": "redis://soloylibre_ultimate_redis_josetusabe:6379", "isDefault": false, "editable": true, "jsonData": {"client": "standalone", "poolSize": 5, "timeout": 10, "pingInterval": 0, "pipelineWindow": 0}, "secureJsonData": {"password": "SoloYLibre2024Redis"}, "version": 1, "uid": "redis-wordpress"}, {"name": "MySQL - WordPress Database", "type": "mysql", "access": "proxy", "url": "soloylibre_ultimate_wordpress_db_jose<PERSON><PERSON>:3306", "database": "soloylibre_ultimate_db", "user": "soloylibre_ultimate", "isDefault": false, "editable": true, "jsonData": {"maxOpenConns": 0, "maxIdleConns": 2, "connMaxLifetime": 14400}, "secureJsonData": {"password": "PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?"}, "version": 1, "uid": "mysql-wordpress"}, {"name": "Docker - Container Metrics", "type": "prometheus", "access": "proxy", "url": "http://cadvisor:8080", "isDefault": false, "editable": true, "jsonData": {"timeInterval": "15s", "queryTimeout": "60s", "httpMethod": "GET"}, "secureJsonData": {}, "version": 1, "uid": "cadvisor-docker"}]}