version: '3.9'

services:
  # PostgreSQL Database - Pre-configured and ready
  database:
    image: postgres:15-alpine
    container_name: soloylibre_database_josetusabe
    hostname: soloylibre-database
    ports:
      - "5433:5432"
    environment:
      TZ: America/New_York
      POSTGRES_DB: soloylibre_db
      POSTGRES_USER: soloylibre_ultimate
      POSTGRES_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      POSTGRES_INITDB_ARGS: "--encoding=UTF8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U soloylibre_ultimate -d soloylibre_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache - Pre-configured
  cache:
    image: redis:7-alpine
    container_name: soloylibre_cache_josetusabe
    hostname: soloylibre-cache
    ports:
      - "6380:6379"
    command: redis-server --requirepass "SoloYLibre2024Redis" --appendonly yes
    environment:
      TZ: America/New_York
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # WordPress - Fully automated setup
  wordpress:
    image: wordpress:php8.2-apache
    container_name: soloylibre_wordpress_josetusabe
    hostname: soloylibre-wordpress
    ports:
      - "1051:80"
    depends_on:
      database:
        condition: service_healthy
      cache:
        condition: service_healthy
    environment:
      TZ: America/New_York
      WORDPRESS_DB_HOST: database
      WORDPRESS_DB_USER: soloylibre_ultimate
      WORDPRESS_DB_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      WORDPRESS_DB_NAME: soloylibre_db
      WORDPRESS_TABLE_PREFIX: wp_soloylibre_
      WORDPRESS_DEBUG: 0
    volumes:
      - wordpress_data:/var/www/html
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/wp-admin/install.php"]
      interval: 30s
      timeout: 10s
      retries: 3

  # phpMyAdmin - Database management
  phpmyadmin:
    image: phpmyadmin:latest
    container_name: soloylibre_phpmyadmin_josetusabe
    hostname: soloylibre-phpmyadmin
    ports:
      - "2051:80"
    depends_on:
      database:
        condition: service_healthy
    environment:
      TZ: America/New_York
      PMA_HOST: database
      PMA_PORT: 5432
      PMA_USER: soloylibre_ultimate
      PMA_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      UPLOAD_LIMIT: 1024M
    restart: unless-stopped

  # Prometheus - Metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre_prometheus_josetusabe
    hostname: soloylibre-prometheus
    ports:
      - "9091:9090"
    environment:
      TZ: America/New_York
    volumes:
      - prometheus_data:/prometheus
      - prometheus_config:/etc/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana - Monitoring dashboard with auto-setup
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_grafana_josetusabe
    hostname: soloylibre-grafana
    ports:
      - "3000:3000"
    depends_on:
      prometheus:
        condition: service_started
    environment:
      TZ: America/New_York
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_DEFAULT_THEME: dark
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-worldmap-panel,redis-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - grafana_provisioning:/etc/grafana/provisioning
    restart: unless-stopped

  # Node Exporter - System metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: soloylibre_node_exporter_josetusabe
    hostname: soloylibre-node-exporter
    ports:
      - "9101:9100"
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    environment:
      TZ: America/New_York
    restart: unless-stopped

  # cAdvisor - Container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: soloylibre_cadvisor_josetusabe
    hostname: soloylibre-cadvisor
    ports:
      - "8081:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    environment:
      TZ: America/New_York
    restart: unless-stopped

  # Auto-Setup Container - Configures everything automatically
  auto-setup:
    image: alpine:latest
    container_name: soloylibre_auto_setup_josetusabe
    hostname: soloylibre-auto-setup
    depends_on:
      wordpress:
        condition: service_healthy
      grafana:
        condition: service_started
      prometheus:
        condition: service_started
    volumes:
      - grafana_provisioning:/etc/grafana/provisioning
      - prometheus_config:/etc/prometheus
      - wordpress_data:/var/www/html
    environment:
      TZ: America/New_York
    command: >
      sh -c "
      echo '🚀 SoloYLibre Ultimate - Automatic Setup Starting...' &&
      echo '👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)' &&
      echo '📧 Email: <EMAIL>' &&
      echo '📱 Phone: ************' &&
      echo '📍 Location: San Jose de Ocoa, Dom. Rep.' &&
      echo '🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB' &&
      echo '' &&
      
      # Install required tools
      apk add --no-cache curl wget &&
      
      # Wait for services to be ready
      echo '⏳ Waiting for services to be ready...' &&
      sleep 60 &&
      
      # Configure Prometheus
      echo '📊 Configuring Prometheus...' &&
      mkdir -p /etc/prometheus &&
      cat > /etc/prometheus/prometheus.yml << 'EOF'
      global:
        scrape_interval: 15s
        evaluation_interval: 15s
        external_labels:
          monitor: 'soloylibre-ultimate'
          owner: 'josetusabe'
      
      scrape_configs:
        - job_name: 'prometheus'
          static_configs:
            - targets: ['localhost:9090']
        
        - job_name: 'node-exporter'
          static_configs:
            - targets: ['soloylibre-node-exporter:9100']
        
        - job_name: 'cadvisor'
          static_configs:
            - targets: ['soloylibre-cadvisor:8080']
        
        - job_name: 'wordpress'
          static_configs:
            - targets: ['soloylibre-wordpress:80']
          metrics_path: '/wp-admin/admin-ajax.php'
          params:
            action: ['heartbeat']
      EOF
      
      # Configure Grafana datasources
      echo '📈 Configuring Grafana datasources...' &&
      mkdir -p /etc/grafana/provisioning/datasources &&
      cat > /etc/grafana/provisioning/datasources/datasources.yml << 'EOF'
      apiVersion: 1
      datasources:
        - name: Prometheus
          type: prometheus
          access: proxy
          url: http://soloylibre-prometheus:9090
          isDefault: true
          editable: true
          jsonData:
            timeInterval: '5s'
            queryTimeout: '60s'
            httpMethod: 'POST'
          version: 1
        
        - name: Redis
          type: redis-datasource
          access: proxy
          url: redis://soloylibre-cache:6379
          isDefault: false
          editable: true
          secureJsonData:
            password: 'SoloYLibre2024Redis'
          version: 1
      EOF
      
      # Configure Grafana dashboards
      echo '📊 Setting up Grafana dashboards...' &&
      mkdir -p /etc/grafana/provisioning/dashboards &&
      cat > /etc/grafana/provisioning/dashboards/dashboards.yml << 'EOF'
      apiVersion: 1
      providers:
        - name: 'SoloYLibre Dashboards'
          orgId: 1
          folder: 'SoloYLibre'
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards
      EOF
      
      # Install WordPress plugins automatically
      echo '🔌 Installing WordPress plugins...' &&
      cd /var/www/html &&
      
      # Download WP-CLI
      curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/phar/wp-cli.phar &&
      chmod +x wp-cli.phar &&
      mv wp-cli.phar /usr/local/bin/wp &&
      
      # Wait for WordPress to be fully ready
      sleep 120 &&
      
      # Install WordPress core
      wp core install --url='http://localhost:1051' --title='SoloYLibre Ultimate - Jose L Encarnacion' --admin_user='soloylibre_ultimate' --admin_password='PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?' --admin_email='<EMAIL>' --allow-root --skip-email &&
      
      # Install essential plugins
      wp plugin install redis-cache --activate --allow-root &&
      wp plugin install contact-form-7 --activate --allow-root &&
      wp plugin install elementor --activate --allow-root &&
      wp plugin install woocommerce --activate --allow-root &&
      
      # Configure Redis cache
      wp redis enable --allow-root &&
      
      # Create sample content
      wp post create --post_type=page --post_title='About SoloYLibre Ultimate' --post_content='<h1>Welcome to SoloYLibre Ultimate</h1><p><strong>Owner:</strong> Jose L Encarnacion (JoseTusabe)</p><p><strong>Email:</strong> <EMAIL></p><p><strong>Phone:</strong> ************</p><p><strong>Location:</strong> San Jose de Ocoa, Dom. Rep.</p><p><strong>Server:</strong> Synology RS3618xs | 56GB RAM | 36TB</p><p><strong>Passion:</strong> Photography and Technology</p><h2>Features</h2><ul><li>Complete WordPress Platform</li><li>Grafana Monitoring</li><li>E-commerce Ready</li><li>Photography Portfolio</li><li>JEYKO AI Development</li></ul>' --post_status=publish --allow-root &&
      
      echo '✅ WordPress setup completed!' &&
      echo '✅ Grafana configuration completed!' &&
      echo '✅ Prometheus configuration completed!' &&
      echo '✅ All services connected and ready!' &&
      echo '' &&
      echo '🎉 SoloYLibre Ultimate Installation Complete!' &&
      echo '' &&
      echo '🌐 ACCESS INFORMATION:' &&
      echo '   WordPress: http://localhost:1051' &&
      echo '   WordPress Admin: http://localhost:1051/wp-admin' &&
      echo '   Grafana: http://localhost:3000' &&
      echo '   phpMyAdmin: http://localhost:2051' &&
      echo '   Prometheus: http://localhost:9090' &&
      echo '' &&
      echo '🔐 LOGIN CREDENTIALS:' &&
      echo '   Username: soloylibre_ultimate' &&
      echo '   Password: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?' &&
      echo '   Email: <EMAIL>' &&
      echo '' &&
      echo '👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)' &&
      echo '📧 Email: <EMAIL>' &&
      echo '📱 Phone: ************' &&
      echo '📍 Location: San Jose de Ocoa, Dom. Rep.' &&
      echo '🖥️ Server: Synology RS3618xs | 56GB RAM | 36TB' &&
      echo '' &&
      echo '🚀 Platform ready for your photography business and JEYKO AI development!' &&
      
      # Keep container running to show logs
      tail -f /dev/null
      "
    restart: "no"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  wordpress_data:
    driver: local
  grafana_data:
    driver: local
  grafana_provisioning:
    driver: local
  prometheus_data:
    driver: local
  prometheus_config:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_network
