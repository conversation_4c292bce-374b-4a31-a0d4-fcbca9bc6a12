version: '3.9'

services:
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre_ultimate_grafana_josetusabe
    hostname: soloylibre-ultimate-grafana
    healthcheck:
      test: curl -f http://localhost:3000/ || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    ports:
      - 3000:3000
    volumes:
      - grafana_data:/var/lib/grafana:rw
      - grafana_config:/etc/grafana:rw
      - grafana_logs:/var/log/grafana:rw
      - grafana_plugins:/var/lib/grafana/plugins:rw
      - grafana_dashboards:/var/lib/grafana/dashboards:rw
    environment:
      # Timezone
      TZ: America/New_York
      
      # Admin Credentials - SoloYLibre Ultimate
      GF_SECURITY_ADMIN_USER: soloylibre_ultimate
      GF_SECURITY_ADMIN_PASSWORD: PxARfV)yC]0ZN:*ysxbnQHtZ+XQ5W2P:Zf>#A+0^BBV+atMs,?
      GF_SECURITY_ADMIN_EMAIL: <EMAIL>
      
      # Server Configuration
      GF_SERVER_DOMAIN: grafana.soloylibre.com
      GF_SERVER_ROOT_URL: https://grafana.soloylibre.com
      GF_SERVER_SERVE_FROM_SUB_PATH: false
      GF_SERVER_HTTP_PORT: 3000
      
      # Security Settings
      GF_SECURITY_DISABLE_GRAVATAR: true
      GF_SECURITY_COOKIE_SECURE: false
      GF_SECURITY_COOKIE_SAMESITE: lax
      GF_SECURITY_ALLOW_EMBEDDING: false
      
      # User Management
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_ALLOW_ORG_CREATE: false
      GF_USERS_AUTO_ASSIGN_ORG: true
      GF_USERS_AUTO_ASSIGN_ORG_ROLE: Viewer
      GF_USERS_DEFAULT_THEME: dark
      
      # Analytics & Reporting
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
      GF_ANALYTICS_GOOGLE_ANALYTICS_UA_ID: ""
      
      # Logging
      GF_LOG_LEVEL: info
      GF_LOG_MODE: console,file
      
      # Paths
      GF_PATHS_DATA: /var/lib/grafana
      GF_PATHS_LOGS: /var/log/grafana
      GF_PATHS_PLUGINS: /var/lib/grafana/plugins
      GF_PATHS_PROVISIONING: /etc/grafana/provisioning
      
      # Plugins Installation
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel,redis-datasource,grafana-piechart-panel,grafana-polystat-panel
      
      # Feature Toggles
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards,tempoSearch,tempoBackendSearch
      
      # Database (SQLite by default)
      GF_DATABASE_TYPE: sqlite3
      GF_DATABASE_PATH: /var/lib/grafana/grafana.db
      
      # Session
      GF_SESSION_PROVIDER: file
      GF_SESSION_PROVIDER_CONFIG: sessions
      
      # SMTP (Optional - for email notifications)
      GF_SMTP_ENABLED: false
      GF_SMTP_HOST: smtp.gmail.com:587
      GF_SMTP_USER: <EMAIL>
      GF_SMTP_FROM_ADDRESS: <EMAIL>
      GF_SMTP_FROM_NAME: SoloYLibre Grafana
      
      # Alerting
      GF_ALERTING_ENABLED: true
      GF_ALERTING_EXECUTE_ALERTS: true
      
      # Unified Alerting
      GF_UNIFIED_ALERTING_ENABLED: true
      
      # Custom Branding
      GF_SERVER_STATIC_ROOT_PATH: /usr/share/grafana
      
      # Owner Information (Custom)
      GRAFANA_OWNER_NAME: "Jose L Encarnacion (JoseTusabe)"
      GRAFANA_OWNER_EMAIL: "<EMAIL>"
      GRAFANA_OWNER_PHONE: "************"
      GRAFANA_OWNER_LOCATION: "San Jose de Ocoa, Dom. Rep."
      GRAFANA_SERVER_INFO: "Synology RS3618xs | 56GB RAM | 36TB Storage"
      
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.soloylibre.com`)"
      - "traefik.http.routers.grafana.entrypoints=websecure"
      - "traefik.http.routers.grafana.tls.certresolver=letsencrypt"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"
      - "com.soloylibre.owner=josetusabe"
      - "com.soloylibre.service=grafana"
      - "com.soloylibre.environment=development"
    restart: on-failure:5

  grafana-setup:
    image: alpine:latest
    container_name: soloylibre_ultimate_grafana_setup_josetusabe
    hostname: soloylibre-ultimate-grafana-setup
    depends_on:
      grafana:
        condition: service_healthy
    volumes:
      - grafana_config:/etc/grafana:rw
      - grafana_dashboards:/var/lib/grafana/dashboards:rw
    environment:
      TZ: America/New_York
    command: >
      sh -c "
      echo '🚀 Setting up SoloYLibre Ultimate Grafana Configuration...' &&
      apk add --no-cache curl &&
      
      # Create provisioning directories
      mkdir -p /etc/grafana/provisioning/datasources &&
      mkdir -p /etc/grafana/provisioning/dashboards &&
      mkdir -p /var/lib/grafana/dashboards &&
      
      # Create datasources configuration
      cat > /etc/grafana/provisioning/datasources/datasources.yml << 'EOF'
      apiVersion: 1
      datasources:
        - name: Prometheus (Local)
          type: prometheus
          access: proxy
          url: http://localhost:9090
          isDefault: true
          editable: true
          jsonData:
            timeInterval: '5s'
            queryTimeout: '60s'
            httpMethod: 'POST'
          version: 1
          uid: prometheus-local
        - name: Prometheus (External)
          type: prometheus
          access: proxy
          url: http://prometheus:9090
          isDefault: false
          editable: true
          jsonData:
            timeInterval: '15s'
            queryTimeout: '60s'
            httpMethod: 'GET'
          version: 1
          uid: prometheus-external
      EOF
      
      # Create dashboard provisioning
      cat > /etc/grafana/provisioning/dashboards/dashboards.yml << 'EOF'
      apiVersion: 1
      providers:
        - name: 'SoloYLibre Ultimate Dashboards'
          orgId: 1
          folder: 'SoloYLibre'
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards
        - name: 'System Monitoring'
          orgId: 1
          folder: 'System'
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards/system
        - name: 'Photography Workflows'
          orgId: 1
          folder: 'Photography'
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards/photography
      EOF
      
      # Create sample dashboard
      cat > /var/lib/grafana/dashboards/soloylibre-welcome.json << 'EOF'
      {
        \"dashboard\": {
          \"id\": null,
          \"title\": \"SoloYLibre Ultimate - Welcome Dashboard\",
          \"description\": \"Welcome dashboard for Jose L Encarnacion (JoseTusabe)\",
          \"tags\": [\"soloylibre\", \"josetusabe\", \"welcome\"],
          \"timezone\": \"America/New_York\",
          \"panels\": [
            {
              \"id\": 1,
              \"title\": \"Welcome to SoloYLibre Ultimate Grafana\",
              \"type\": \"text\",
              \"gridPos\": {\"h\": 8, \"w\": 24, \"x\": 0, \"y\": 0},
              \"options\": {
                \"mode\": \"markdown\",
                \"content\": \"# 🎉 Welcome to SoloYLibre Ultimate Grafana!\\n\\n**Owner:** Jose L Encarnacion (JoseTusabe)  \\n**Email:** <EMAIL>  \\n**Phone:** ************  \\n**Location:** San Jose de Ocoa, Dom. Rep.  \\n**Server:** Synology RS3618xs | 56GB RAM | 36TB Storage\\n\\n## 🌐 Websites\\n- [SoloYLibre](https://soloylibre.com)\\n- [JoseTusabe](https://josetusabe.com)\\n- [1and1Photo](https://1and1photo.com)\\n- [Jose L Encarnacion](https://joselencarnacion.com)\\n\\n## 🎯 JEYKO AI Development Environment\\nThis Grafana instance is optimized for monitoring and analytics in the JEYKO AI development workflow.\\n\\n**Passionate about Photography and Technology** 📸💻\"
              }
            }
          ],
          \"time\": {\"from\": \"now-1h\", \"to\": \"now\"},
          \"refresh\": \"5s\"
        }
      }
      EOF
      
      echo '✅ SoloYLibre Ultimate Grafana setup completed!' &&
      echo '📊 Dashboard: http://localhost:3000' &&
      echo '🔐 Username: soloylibre_ultimate' &&
      echo '👨‍💻 Owner: Jose L Encarnacion (JoseTusabe)' &&
      sleep 30
      "
    restart: "no"

volumes:
  grafana_data:
    driver: local
  grafana_config:
    driver: local
  grafana_logs:
    driver: local
  grafana_plugins:
    driver: local
  grafana_dashboards:
    driver: local

networks:
  default:
    driver: bridge
    name: soloylibre_ultimate_grafana_network
